'use strict';

/**
 * 数据库索引优化迁移
 * 为Wolf Fun游戏项目添加性能优化索引
 * 重点优化农场和配送线相关的高频查询
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('开始执行数据库索引优化...');

    try {
      // 辅助函数：检查索引是否存在
      const indexExists = async (tableName, indexName) => {
        try {
          const indexes = await queryInterface.showIndex(tableName);
          return indexes.some(index => index.name === indexName);
        } catch (error) {
          return false;
        }
      };

      // 辅助函数：安全添加索引
      const safeAddIndex = async (tableName, columns, options) => {
        const exists = await indexExists(tableName, options.name);
        if (!exists) {
          try {
            await queryInterface.addIndex(tableName, columns, options);
            console.log(`✓ 已添加索引: ${tableName}.${options.name}`);
          } catch (error) {
            console.warn(`⚠ 添加索引失败 ${tableName}.${options.name}:`, error.message);
          }
        } else {
          console.log(`- 索引已存在: ${tableName}.${options.name}`);
        }
      };

      // 1. 优化 farm_plots 表索引
      console.log('优化 farm_plots 表索引...');

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();

      if (tables.includes('farm_plots')) {
        // 为 walletId 添加单独索引（高频查询）
        await safeAddIndex('farm_plots', ['walletId'], {
          name: 'idx_farm_plots_wallet_id',
          concurrently: true
        });

        // 为 isUnlocked 添加索引（用于筛选已解锁的农场）
        await safeAddIndex('farm_plots', ['isUnlocked'], {
          name: 'idx_farm_plots_is_unlocked',
          concurrently: true
        });

        // 为 walletId + isUnlocked 添加复合索引（最高频查询）
        await safeAddIndex('farm_plots', ['walletId', 'isUnlocked'], {
          name: 'idx_farm_plots_wallet_unlocked',
          concurrently: true
        });

        // 为 lastProductionTime 添加索引（用于离线收益计算）
        await safeAddIndex('farm_plots', ['lastProductionTime'], {
          name: 'idx_farm_plots_last_production_time',
          concurrently: true
        });

        // 为 level 添加索引（用于排行榜和统计）
        await safeAddIndex('farm_plots', ['level'], {
          name: 'idx_farm_plots_level',
          concurrently: true
        });
      }

      // 2. 优化 delivery_lines 表索引
      console.log('优化 delivery_lines 表索引...');
      
      if (tables.includes('delivery_lines')) {
        // 为 walletId 添加唯一索引（每个用户只有一条配送线）
        await safeAddIndex('delivery_lines', ['walletId'], {
          unique: true,
          name: 'idx_delivery_lines_wallet_id_unique',
          concurrently: true
        });

        // 为 lastDeliveryTime 添加索引（用于离线收益计算）
        await safeAddIndex('delivery_lines', ['lastDeliveryTime'], {
          name: 'idx_delivery_lines_last_delivery_time',
          concurrently: true
        });

        // 为 level 添加索引（用于统计和排行）
        await safeAddIndex('delivery_lines', ['level'], {
          name: 'idx_delivery_lines_level',
          concurrently: true
        });

        // 为 pendingBlocks 添加索引（用于筛选有待处理方块的配送线）
        await safeAddIndex('delivery_lines', ['pendingBlocks'], {
          name: 'idx_delivery_lines_pending_blocks',
          concurrently: true
        });
      }

      // 3. 优化 user_wallets 表索引
      console.log('优化 user_wallets 表索引...');
      
      if (tables.includes('user_wallets')) {
        // 为 gem 添加索引（用于排行榜）
        await safeAddIndex('user_wallets', ['gem'], {
          name: 'idx_user_wallets_gem',
          concurrently: true
        });

        // 为 milk 添加索引（用于统计和筛选）
        await safeAddIndex('user_wallets', ['milk'], {
          name: 'idx_user_wallets_milk',
          concurrently: true
        });

        // 为 userId 添加索引（用于关联查询）
        await safeAddIndex('user_wallets', ['userId'], {
          name: 'idx_user_wallets_user_id',
          concurrently: true
        });

        // 为 lastActiveTime 添加索引（用于离线时间计算）
        const userWalletDesc = await queryInterface.describeTable('user_wallets');
        if (userWalletDesc.lastActiveTime) {
          await safeAddIndex('user_wallets', ['lastActiveTime'], {
            name: 'idx_user_wallets_last_active_time',
            concurrently: true
          });
        }
      }

      // 4. 优化 vip_memberships 表索引（如果存在）
      console.log('优化 vip_memberships 表索引...');

      if (tables.includes('vip_memberships')) {
        // 检查表结构
        const vipTableDesc = await queryInterface.describeTable('vip_memberships');

        // 为 walletId 添加唯一索引
        if (vipTableDesc.walletId) {
          await safeAddIndex('vip_memberships', ['walletId'], {
            unique: true,
            name: 'idx_vip_memberships_wallet_id_unique',
            concurrently: true
          });
        }

        // 为 expiresAt 添加索引（用于检查VIP状态）
        if (vipTableDesc.expiresAt) {
          await safeAddIndex('vip_memberships', ['expiresAt'], {
            name: 'idx_vip_memberships_expires_at',
            concurrently: true
          });
        }

        // 为 isActive 添加索引（用于筛选活跃VIP）
        if (vipTableDesc.isActive) {
          await safeAddIndex('vip_memberships', ['isActive'], {
            name: 'idx_vip_memberships_is_active',
            concurrently: true
          });
        }
      }

      // 5. 优化 active_boosters 表索引（如果存在）
      console.log('优化 active_boosters 表索引...');

      if (tables.includes('active_boosters')) {
        // 检查表结构
        const boosterTableDesc = await queryInterface.describeTable('active_boosters');

        // 为 walletId 添加索引
        if (boosterTableDesc.walletId) {
          await safeAddIndex('active_boosters', ['walletId'], {
            name: 'idx_active_boosters_wallet_id',
            concurrently: true
          });
        }

        // 为 expiresAt 添加索引（用于检查加速器状态）
        if (boosterTableDesc.expiresAt) {
          await safeAddIndex('active_boosters', ['expiresAt'], {
            name: 'idx_active_boosters_expires_at',
            concurrently: true
          });
        }

        // 为 walletId + expiresAt 添加复合索引（高频查询）
        if (boosterTableDesc.walletId && boosterTableDesc.expiresAt) {
          await safeAddIndex('active_boosters', ['walletId', 'expiresAt'], {
            name: 'idx_active_boosters_wallet_expires',
            concurrently: true
          });
        }
      }

      console.log('数据库索引优化完成！');

    } catch (error) {
      console.error('数据库索引优化失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('开始回滚数据库索引优化...');

    try {
      // 删除所有添加的索引
      const indexesToRemove = [
        // farm_plots 索引
        { table: 'farm_plots', name: 'idx_farm_plots_wallet_id' },
        { table: 'farm_plots', name: 'idx_farm_plots_is_unlocked' },
        { table: 'farm_plots', name: 'idx_farm_plots_wallet_unlocked' },
        { table: 'farm_plots', name: 'idx_farm_plots_last_production_time' },
        { table: 'farm_plots', name: 'idx_farm_plots_level' },
        
        // delivery_lines 索引
        { table: 'delivery_lines', name: 'idx_delivery_lines_wallet_id_unique' },
        { table: 'delivery_lines', name: 'idx_delivery_lines_last_delivery_time' },
        { table: 'delivery_lines', name: 'idx_delivery_lines_level' },
        { table: 'delivery_lines', name: 'idx_delivery_lines_pending_blocks' },
        
        // user_wallets 索引
        { table: 'user_wallets', name: 'idx_user_wallets_gem' },
        { table: 'user_wallets', name: 'idx_user_wallets_milk' },
        { table: 'user_wallets', name: 'idx_user_wallets_user_id' },
        { table: 'user_wallets', name: 'idx_user_wallets_last_active_time' },
        
        // vip_memberships 索引
        { table: 'vip_memberships', name: 'idx_vip_memberships_wallet_id_unique' },
        { table: 'vip_memberships', name: 'idx_vip_memberships_expires_at' },
        { table: 'vip_memberships', name: 'idx_vip_memberships_is_active' },
        
        // active_boosters 索引
        { table: 'active_boosters', name: 'idx_active_boosters_wallet_id' },
        { table: 'active_boosters', name: 'idx_active_boosters_expires_at' },
        { table: 'active_boosters', name: 'idx_active_boosters_wallet_expires' }
      ];

      const tables = await queryInterface.showAllTables();

      for (const { table, name } of indexesToRemove) {
        if (tables.includes(table)) {
          try {
            await queryInterface.removeIndex(table, name);
            console.log(`已删除索引: ${table}.${name}`);
          } catch (error) {
            console.warn(`删除索引失败 ${table}.${name}:`, error.message);
          }
        }
      }

      console.log('数据库索引优化回滚完成！');

    } catch (error) {
      console.error('数据库索引优化回滚失败:', error);
      throw error;
    }
  }
};
