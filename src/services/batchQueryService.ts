/**
 * 批量查询优化服务
 * 用于优化批量数据库操作，减少查询次数，提升性能
 */

import { sequelize } from '../config/db';
import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { UserWallet } from '../models/UserWallet';
import { VipMembership } from '../models/VipMembership';
import { ActiveBooster } from '../models/ActiveBooster';
import { Op } from 'sequelize';

interface BatchUpdateResult {
  success: boolean;
  affectedRows: number;
  errors?: string[];
}

interface UserGameData {
  walletId: number;
  userWallet: UserWallet;
  farmPlots: FarmPlot[];
  deliveryLine: DeliveryLine;
  vipEffects: any;
  boosterEffects: any;
}

class BatchQueryService {
  
  /**
   * 批量获取用户游戏数据
   * 一次查询获取多个用户的完整游戏状态
   */
  async batchGetUserGameData(walletIds: number[]): Promise<Map<number, UserGameData>> {
    if (walletIds.length === 0) {
      return new Map();
    }

    try {
      // 并行执行所有查询
      const [userWallets, farmPlots, deliveryLines, vipMemberships, activeBoosters] = await Promise.all([
        // 批量获取用户钱包
        UserWallet.findAll({
          where: { id: { [Op.in]: walletIds } }
        }),
        
        // 批量获取农场区
        FarmPlot.findAll({
          where: { walletId: { [Op.in]: walletIds } },
          order: [['walletId', 'ASC'], ['plotNumber', 'ASC']]
        }),
        
        // 批量获取配送线
        DeliveryLine.findAll({
          where: { walletId: { [Op.in]: walletIds } }
        }),
        
        // 批量获取VIP状态
        VipMembership.findAll({
          where: { walletId: { [Op.in]: walletIds } }
        }),
        
        // 批量获取活跃加速器
        ActiveBooster.findAll({
          where: { 
            walletId: { [Op.in]: walletIds },
            expiresAt: { [Op.gt]: new Date() }
          },
          order: [['walletId', 'ASC'], ['expiresAt', 'DESC']]
        })
      ]);

      // 构建数据映射
      const userWalletMap = new Map(userWallets.map(wallet => [wallet.id, wallet]));
      const deliveryLineMap = new Map(deliveryLines.map(line => [line.walletId, line]));
      const vipMap = new Map(vipMemberships.map(vip => [vip.walletId, vip]));
      
      // 按用户分组农场区
      const farmPlotsMap = new Map<number, FarmPlot[]>();
      farmPlots.forEach(plot => {
        if (!farmPlotsMap.has(plot.walletId)) {
          farmPlotsMap.set(plot.walletId, []);
        }
        farmPlotsMap.get(plot.walletId)!.push(plot);
      });

      // 按用户分组活跃加速器
      const boosterMap = new Map<number, ActiveBooster>();
      activeBoosters.forEach(booster => {
        if (!boosterMap.has(booster.walletId)) {
          boosterMap.set(booster.walletId, booster);
        }
      });

      // 构建最终结果
      const result = new Map<number, UserGameData>();
      
      for (const walletId of walletIds) {
        const userWallet = userWalletMap.get(walletId);
        if (!userWallet) continue;

        const userFarmPlots = farmPlotsMap.get(walletId) || [];
        const userDeliveryLine = deliveryLineMap.get(walletId);
        if (!userDeliveryLine) continue;

        // 计算VIP效果
        const vipMembership = vipMap.get(walletId);
        const vipEffects = this.calculateVipEffects(vipMembership);

        // 计算加速器效果
        const activeBooster = boosterMap.get(walletId);
        const boosterEffects = this.calculateBoosterEffects(activeBooster);

        result.set(walletId, {
          walletId,
          userWallet,
          farmPlots: userFarmPlots,
          deliveryLine: userDeliveryLine,
          vipEffects,
          boosterEffects
        });
      }

      return result;

    } catch (error) {
      console.error('批量获取用户游戏数据失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新农场区数据
   */
  async batchUpdateFarmPlots(updates: Array<{
    id: number;
    data: Partial<FarmPlot>;
  }>): Promise<BatchUpdateResult> {
    if (updates.length === 0) {
      return { success: true, affectedRows: 0 };
    }

    const transaction = await sequelize.transaction();
    
    try {
      let totalAffectedRows = 0;
      const errors: string[] = [];

      // 分批处理更新，避免单次更新过多数据
      const batchSize = 100;
      for (let i = 0; i < updates.length; i += batchSize) {
        const batch = updates.slice(i, i + batchSize);
        
        for (const update of batch) {
          try {
            const [affectedRows] = await FarmPlot.update(update.data, {
              where: { id: update.id },
              transaction
            });
            totalAffectedRows += affectedRows;
          } catch (error) {
            errors.push(`更新农场区 ${update.id} 失败: ${error}`);
          }
        }
      }

      await transaction.commit();
      
      return {
        success: errors.length === 0,
        affectedRows: totalAffectedRows,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 批量更新用户钱包余额
   */
  async batchUpdateUserWallets(updates: Array<{
    walletId: number;
    gemDelta?: number;
    milkDelta?: number;
  }>): Promise<BatchUpdateResult> {
    if (updates.length === 0) {
      return { success: true, affectedRows: 0 };
    }

    const transaction = await sequelize.transaction();
    
    try {
      let totalAffectedRows = 0;
      const errors: string[] = [];

      for (const update of updates) {
        try {
          const updateData: any = {};
          
          if (update.gemDelta !== undefined) {
            updateData.gem = sequelize.literal(`gem + ${update.gemDelta}`);
          }
          
          if (update.milkDelta !== undefined) {
            updateData.milk = sequelize.literal(`milk + ${update.milkDelta}`);
          }

          const [affectedRows] = await UserWallet.update(updateData, {
            where: { id: update.walletId },
            transaction
          });
          
          totalAffectedRows += affectedRows;
        } catch (error) {
          errors.push(`更新用户钱包 ${update.walletId} 失败: ${error}`);
        }
      }

      await transaction.commit();
      
      return {
        success: errors.length === 0,
        affectedRows: totalAffectedRows,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 批量计算离线收益
   */
  async batchCalculateOfflineEarnings(walletIds: number[], currentTime: Date): Promise<Map<number, {
    milkEarned: number;
    gemsEarned: number;
    offlineSeconds: number;
  }>> {
    const gameDataMap = await this.batchGetUserGameData(walletIds);
    const results = new Map();

    for (const [walletId, gameData] of gameDataMap) {
      try {
        const { farmPlots, deliveryLine, vipEffects, boosterEffects } = gameData;
        
        // 计算离线时间
        const lastActiveTime = gameData.userWallet.lastActiveTime || new Date();
        const offlineSeconds = Math.max(0, Math.floor((currentTime.getTime() - lastActiveTime.getTime()) / 1000));
        
        if (offlineSeconds === 0) {
          results.set(walletId, { milkEarned: 0, gemsEarned: 0, offlineSeconds: 0 });
          continue;
        }

        // 计算农场区离线产奶
        let totalMilkEarned = 0;
        const unlockedPlots = farmPlots.filter(plot => plot.isUnlocked);
        
        for (const plot of unlockedPlots) {
          const actualProductionSpeed = plot.productionSpeed / vipEffects.productionSpeedMultiplier;
          const cycles = Math.floor(offlineSeconds / actualProductionSpeed);
          const milkProduced = cycles * plot.milkProduction * plot.barnCount;
          totalMilkEarned += milkProduced;
        }

        // 计算配送线离线收益
        const actualDeliverySpeed = deliveryLine.deliverySpeed / (vipEffects.deliverySpeedMultiplier * boosterEffects.speedMultiplier);
        const actualBlockPrice = deliveryLine.blockPrice * vipEffects.blockPriceMultiplier;
        
        const deliveryCycles = Math.floor(offlineSeconds / actualDeliverySpeed);
        const blocksDelivered = Math.min(deliveryCycles, deliveryLine.pendingBlocks);
        const gemsEarned = blocksDelivered * actualBlockPrice;

        results.set(walletId, {
          milkEarned: totalMilkEarned,
          gemsEarned,
          offlineSeconds
        });

      } catch (error) {
        console.error(`计算用户 ${walletId} 离线收益失败:`, error);
        results.set(walletId, { milkEarned: 0, gemsEarned: 0, offlineSeconds: 0 });
      }
    }

    return results;
  }

  /**
   * 计算VIP效果
   */
  private calculateVipEffects(vipMembership?: VipMembership): any {
    if (!vipMembership || !vipMembership.checkAndUpdateStatus()) {
      return {
        isVip: false,
        deliverySpeedMultiplier: 1,
        blockPriceMultiplier: 1,
        productionSpeedMultiplier: 1
      };
    }

    return {
      isVip: true,
      deliverySpeedMultiplier: 1.3, // VIP 30% 出货线速度加成
      blockPriceMultiplier: 1.2,    // VIP 20% 出货线价格加成
      productionSpeedMultiplier: 1.3 // VIP 30% 牧场区生产速度加成
    };
  }

  /**
   * 计算加速器效果
   */
  private calculateBoosterEffects(activeBooster?: ActiveBooster): any {
    if (!activeBooster) {
      return {
        hasBooster: false,
        speedMultiplier: 1,
        boosterType: null
      };
    }

    const speedMultiplier = activeBooster.productId === 'speed_boost_2x' ? 2 : 
                           activeBooster.productId === 'speed_boost_4x' ? 4 : 1;

    return {
      hasBooster: true,
      speedMultiplier,
      boosterType: activeBooster.productId,
      expiresAt: activeBooster.expiresAt
    };
  }

  /**
   * 获取数据库连接池状态
   */
  async getConnectionPoolStatus(): Promise<any> {
    try {
      const pool = sequelize.connectionManager.pool;
      return {
        size: pool.size,
        available: pool.available,
        using: pool.using,
        waiting: pool.waiting
      };
    } catch (error) {
      console.error('获取连接池状态失败:', error);
      return null;
    }
  }
}

export default new BatchQueryService();
