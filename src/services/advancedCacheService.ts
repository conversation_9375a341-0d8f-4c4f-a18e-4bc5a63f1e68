/**
 * 高级Redis缓存策略优化服务
 * 实现多层缓存、缓存预热、失效策略、穿透防护等高级功能
 */

import { redis } from '../config/redis';
import { EventEmitter } from 'events';

interface CacheConfig {
  ttl: number;
  maxSize?: number;
  compressionThreshold?: number;
  enableCompression?: boolean;
  enableDistributedLock?: boolean;
}

interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  hitRate: number;
  totalOperations: number;
}

interface DistributedLockOptions {
  timeout: number;
  retryDelay: number;
  maxRetries: number;
}

class AdvancedCacheService extends EventEmitter {
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
    hitRate: 0,
    totalOperations: 0
  };

  private localCache = new Map<string, { data: any; expires: number }>();
  private maxLocalCacheSize = 1000;
  private compressionThreshold = 1024; // 1KB

  /**
   * 多层缓存获取
   * 先检查本地缓存，再检查Redis缓存
   */
  async get<T>(key: string, useLocalCache: boolean = true): Promise<T | null> {
    try {
      // 第一层：本地内存缓存
      if (useLocalCache) {
        const localData = this.getFromLocalCache<T>(key);
        if (localData !== null) {
          this.stats.hits++;
          this.updateStats();
          return localData;
        }
      }

      // 第二层：Redis缓存
      const redisData = await redis.get(key);
      if (redisData) {
        this.stats.hits++;
        let parsedData: T;
        
        try {
          // 检查是否是压缩数据
          if (redisData.startsWith('COMPRESSED:')) {
            const compressedData = redisData.substring(11);
            const decompressed = await this.decompress(compressedData);
            parsedData = JSON.parse(decompressed);
          } else {
            parsedData = JSON.parse(redisData);
          }
          
          // 回填到本地缓存
          if (useLocalCache) {
            this.setToLocalCache(key, parsedData, 60); // 本地缓存1分钟
          }
          
          this.updateStats();
          return parsedData;
        } catch (parseError) {
          console.error('缓存数据解析失败:', parseError);
          await this.delete(key); // 删除损坏的缓存
          this.stats.errors++;
        }
      }

      this.stats.misses++;
      this.updateStats();
      return null;

    } catch (error) {
      console.error('缓存获取失败:', error);
      this.stats.errors++;
      this.updateStats();
      return null;
    }
  }

  /**
   * 多层缓存设置
   */
  async set(
    key: string, 
    value: any, 
    config: CacheConfig,
    useLocalCache: boolean = true
  ): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      let dataToStore = serialized;

      // 数据压缩
      if (config.enableCompression && serialized.length > (config.compressionThreshold || this.compressionThreshold)) {
        const compressed = await this.compress(serialized);
        dataToStore = 'COMPRESSED:' + compressed;
      }

      // 设置到Redis
      if (config.ttl > 0) {
        await redis.setex(key, config.ttl, dataToStore);
      } else {
        await redis.set(key, dataToStore);
      }

      // 设置到本地缓存
      if (useLocalCache) {
        this.setToLocalCache(key, value, Math.min(config.ttl, 300)); // 本地缓存最多5分钟
      }

      this.stats.sets++;
      this.updateStats();
      
      // 发出缓存设置事件
      this.emit('cache:set', { key, size: dataToStore.length });
      
      return true;

    } catch (error) {
      console.error('缓存设置失败:', error);
      this.stats.errors++;
      this.updateStats();
      return false;
    }
  }

  /**
   * 分布式锁实现
   */
  async acquireDistributedLock(
    lockKey: string, 
    options: DistributedLockOptions = { timeout: 10000, retryDelay: 100, maxRetries: 100 }
  ): Promise<string | null> {
    const lockValue = `${Date.now()}-${Math.random()}`;
    const lockTimeout = Math.ceil(options.timeout / 1000);

    for (let attempt = 0; attempt < options.maxRetries; attempt++) {
      try {
        const result = await redis.set(lockKey, lockValue, 'PX', options.timeout, 'NX');
        if (result === 'OK') {
          return lockValue;
        }

        // 检查锁是否已过期
        const ttl = await redis.ttl(lockKey);
        if (ttl === -1) {
          // 锁没有过期时间，强制设置过期时间
          await redis.expire(lockKey, lockTimeout);
        }

        await new Promise(resolve => setTimeout(resolve, options.retryDelay));
      } catch (error) {
        console.error('获取分布式锁失败:', error);
      }
    }

    return null;
  }

  /**
   * 释放分布式锁
   */
  async releaseDistributedLock(lockKey: string, lockValue: string): Promise<boolean> {
    try {
      const script = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `;
      
      const result = await redis.eval(script, 1, lockKey, lockValue);
      return result === 1;
    } catch (error) {
      console.error('释放分布式锁失败:', error);
      return false;
    }
  }

  /**
   * 缓存穿透防护
   * 对于不存在的数据，缓存空值防止重复查询
   */
  async getWithBloomFilter<T>(
    key: string,
    fetchFunction: () => Promise<T | null>,
    config: CacheConfig
  ): Promise<T | null> {
    // 先尝试从缓存获取
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // 检查是否是已知的空值
    const nullCacheKey = `null:${key}`;
    const isNull = await redis.get(nullCacheKey);
    if (isNull) {
      return null;
    }

    // 使用分布式锁防止缓存击穿
    const lockKey = `lock:${key}`;
    const lockValue = await this.acquireDistributedLock(lockKey);
    
    if (!lockValue) {
      // 获取锁失败，等待一下再尝试从缓存获取
      await new Promise(resolve => setTimeout(resolve, 50));
      return await this.get<T>(key);
    }

    try {
      // 再次检查缓存（可能在等待锁的过程中被其他进程设置了）
      const cachedAgain = await this.get<T>(key);
      if (cachedAgain !== null) {
        return cachedAgain;
      }

      // 执行实际的数据获取
      const data = await fetchFunction();
      
      if (data !== null) {
        // 缓存有效数据
        await this.set(key, data, config);
      } else {
        // 缓存空值，防止缓存穿透
        await redis.setex(nullCacheKey, 60, '1'); // 空值缓存1分钟
      }

      return data;

    } finally {
      // 释放锁
      await this.releaseDistributedLock(lockKey, lockValue);
    }
  }

  /**
   * 批量缓存预热
   */
  async warmupCache<T>(
    keys: string[],
    fetchFunction: (key: string) => Promise<T | null>,
    config: CacheConfig,
    concurrency: number = 10
  ): Promise<void> {
    console.log(`开始缓存预热，键数量: ${keys.length}`);
    const startTime = Date.now();

    // 分批并发处理
    const batches = [];
    for (let i = 0; i < keys.length; i += concurrency) {
      batches.push(keys.slice(i, i + concurrency));
    }

    let successCount = 0;
    let errorCount = 0;

    for (const batch of batches) {
      const promises = batch.map(async (key) => {
        try {
          // 检查是否已经缓存
          const existing = await this.get(key, false); // 不使用本地缓存
          if (existing !== null) {
            return;
          }

          // 获取数据并缓存
          const data = await fetchFunction(key);
          if (data !== null) {
            await this.set(key, data, config, false); // 不设置本地缓存
            successCount++;
          }
        } catch (error) {
          console.error(`预热缓存失败 ${key}:`, error);
          errorCount++;
        }
      });

      await Promise.all(promises);
    }

    const duration = Date.now() - startTime;
    console.log(`缓存预热完成，成功: ${successCount}，失败: ${errorCount}，耗时: ${duration}ms`);
  }

  /**
   * 智能缓存失效
   * 基于访问模式和数据变化频率智能调整TTL
   */
  async smartInvalidate(pattern: string, options: {
    minTTL?: number;
    maxTTL?: number;
    accessThreshold?: number;
  } = {}): Promise<void> {
    const { minTTL = 60, maxTTL = 3600, accessThreshold = 10 } = options;

    try {
      const keys = await redis.keys(pattern);
      
      for (const key of keys) {
        // 获取键的访问统计（如果有的话）
        const accessCount = await redis.get(`access:${key}`);
        const count = accessCount ? parseInt(accessCount) : 0;

        if (count < accessThreshold) {
          // 访问频率低，缩短TTL或删除
          const currentTTL = await redis.ttl(key);
          if (currentTTL > minTTL) {
            await redis.expire(key, minTTL);
          }
        } else {
          // 访问频率高，延长TTL
          await redis.expire(key, maxTTL);
        }
      }
    } catch (error) {
      console.error('智能缓存失效失败:', error);
    }
  }

  /**
   * 缓存数据压缩
   */
  private async compress(data: string): Promise<string> {
    // 这里可以使用zlib或其他压缩库
    // 为了简化，这里只是base64编码
    return Buffer.from(data).toString('base64');
  }

  /**
   * 缓存数据解压缩
   */
  private async decompress(data: string): Promise<string> {
    // 对应compress方法的解压缩
    return Buffer.from(data, 'base64').toString();
  }

  /**
   * 本地缓存操作
   */
  private getFromLocalCache<T>(key: string): T | null {
    const item = this.localCache.get(key);
    if (item && item.expires > Date.now()) {
      return item.data;
    }
    
    if (item) {
      this.localCache.delete(key);
    }
    
    return null;
  }

  private setToLocalCache(key: string, data: any, ttlSeconds: number): void {
    // 检查本地缓存大小
    if (this.localCache.size >= this.maxLocalCacheSize) {
      // 删除最旧的条目
      const firstKey = this.localCache.keys().next().value;
      if (firstKey) {
        this.localCache.delete(firstKey);
      }
    }

    this.localCache.set(key, {
      data,
      expires: Date.now() + (ttlSeconds * 1000)
    });
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<boolean> {
    try {
      // 删除Redis缓存
      await redis.del(key);
      
      // 删除本地缓存
      this.localCache.delete(key);
      
      this.stats.deletes++;
      this.updateStats();
      
      return true;
    } catch (error) {
      console.error('删除缓存失败:', error);
      this.stats.errors++;
      this.updateStats();
      return false;
    }
  }

  /**
   * 批量删除缓存
   */
  async deletePattern(pattern: string): Promise<number> {
    try {
      const keys = await redis.keys(pattern);
      if (keys.length === 0) {
        return 0;
      }

      await redis.del(...keys);
      
      // 删除匹配的本地缓存
      for (const key of this.localCache.keys()) {
        if (key.includes(pattern.replace('*', ''))) {
          this.localCache.delete(key);
        }
      }

      this.stats.deletes += keys.length;
      this.updateStats();
      
      return keys.length;
    } catch (error) {
      console.error('批量删除缓存失败:', error);
      this.stats.errors++;
      this.updateStats();
      return 0;
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.totalOperations = this.stats.hits + this.stats.misses;
    this.stats.hitRate = this.stats.totalOperations > 0 ? 
      (this.stats.hits / this.stats.totalOperations) * 100 : 0;
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      hitRate: 0,
      totalOperations: 0
    };
  }

  /**
   * 清理本地缓存中的过期条目
   */
  cleanupLocalCache(): void {
    const now = Date.now();
    for (const [key, item] of this.localCache.entries()) {
      if (item.expires <= now) {
        this.localCache.delete(key);
      }
    }
  }

  /**
   * 启动定期清理任务
   */
  startCleanupTask(intervalMs: number = 60000): void {
    setInterval(() => {
      this.cleanupLocalCache();
    }, intervalMs);
  }
}

export default new AdvancedCacheService();
