/**
 * 安全支付验证服务
 * 优化DappPortal支付验证流程的安全性和性能
 */

import crypto from 'crypto';
import { redis } from '../config/redis';
import advancedCacheService from './advancedCacheService';
import { CACHE_STRATEGIES } from '../config/cacheStrategies';

interface PaymentVerificationRequest {
  transactionId: string;
  walletAddress: string;
  productId: string;
  amount: number;
  timestamp: number;
  signature?: string;
}

interface PaymentVerificationResult {
  isValid: boolean;
  transactionId: string;
  productId: string;
  amount: number;
  walletAddress: string;
  verificationTime: number;
  errors?: string[];
  riskScore?: number;
}

interface SecurityConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  signatureRequired: boolean;
  timestampToleranceMs: number;
  enableRiskAssessment: boolean;
  blacklistCheckEnabled: boolean;
}

class SecurePaymentService {
  private securityConfig: SecurityConfig = {
    maxRequestsPerMinute: 10,
    maxRequestsPerHour: 100,
    signatureRequired: true,
    timestampToleranceMs: 300000, // 5分钟
    enableRiskAssessment: true,
    blacklistCheckEnabled: true
  };

  private dappPortalApiKey = process.env.DAPP_PORTAL_API_KEY || '';
  private dappPortalSecret = process.env.DAPP_PORTAL_SECRET || '';
  private dappPortalBaseUrl = process.env.DAPP_PORTAL_BASE_URL || 'https://api.dappportal.com';

  /**
   * 安全的支付验证主方法
   */
  async verifyPayment(request: PaymentVerificationRequest): Promise<PaymentVerificationResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let riskScore = 0;

    try {
      // 1. 输入验证和清理
      const sanitizedRequest = this.sanitizeInput(request);
      if (!sanitizedRequest) {
        return this.createErrorResult(request, ['输入数据无效'], startTime);
      }

      // 2. 防重放攻击检查
      const replayCheck = await this.checkReplayAttack(sanitizedRequest);
      if (!replayCheck.isValid) {
        errors.push(...replayCheck.errors);
        riskScore += 50;
      }

      // 3. 速率限制检查
      const rateLimitCheck = await this.checkRateLimit(sanitizedRequest.walletAddress);
      if (!rateLimitCheck.isValid) {
        errors.push(...rateLimitCheck.errors);
        riskScore += 30;
      }

      // 4. 签名验证
      if (this.securityConfig.signatureRequired) {
        const signatureCheck = this.verifySignature(sanitizedRequest);
        if (!signatureCheck.isValid) {
          errors.push(...signatureCheck.errors);
          riskScore += 40;
        }
      }

      // 5. 时间戳验证
      const timestampCheck = this.verifyTimestamp(sanitizedRequest.timestamp);
      if (!timestampCheck.isValid) {
        errors.push(...timestampCheck.errors);
        riskScore += 20;
      }

      // 6. 黑名单检查
      if (this.securityConfig.blacklistCheckEnabled) {
        const blacklistCheck = await this.checkBlacklist(sanitizedRequest.walletAddress);
        if (!blacklistCheck.isValid) {
          errors.push(...blacklistCheck.errors);
          riskScore += 100; // 黑名单是高风险
        }
      }

      // 7. 风险评估
      if (this.securityConfig.enableRiskAssessment) {
        const riskAssessment = await this.assessRisk(sanitizedRequest);
        riskScore += riskAssessment.score;
        if (riskAssessment.errors.length > 0) {
          errors.push(...riskAssessment.errors);
        }
      }

      // 8. DappPortal API验证
      let dappPortalResult = null;
      if (errors.length === 0 || riskScore < 50) { // 只有在低风险时才调用外部API
        dappPortalResult = await this.verifyWithDappPortal(sanitizedRequest);
        if (!dappPortalResult.isValid) {
          errors.push(...dappPortalResult.errors);
          riskScore += 60;
        }
      }

      // 9. 记录验证结果
      await this.logVerificationAttempt(sanitizedRequest, errors, riskScore);

      const verificationTime = Date.now() - startTime;
      const isValid = errors.length === 0 && riskScore < 50;

      return {
        isValid,
        transactionId: sanitizedRequest.transactionId,
        productId: sanitizedRequest.productId,
        amount: sanitizedRequest.amount,
        walletAddress: sanitizedRequest.walletAddress,
        verificationTime,
        errors: errors.length > 0 ? errors : undefined,
        riskScore
      };

    } catch (error) {
      console.error('支付验证过程中发生错误:', error);
      return this.createErrorResult(request, ['验证过程中发生内部错误'], startTime);
    }
  }

  /**
   * 输入数据清理和验证
   */
  private sanitizeInput(request: PaymentVerificationRequest): PaymentVerificationRequest | null {
    try {
      // 验证必需字段
      if (!request.transactionId || !request.walletAddress || !request.productId) {
        return null;
      }

      // 清理和验证数据
      const sanitized: PaymentVerificationRequest = {
        transactionId: request.transactionId.trim().substring(0, 100), // 限制长度
        walletAddress: request.walletAddress.trim().toLowerCase(),
        productId: request.productId.trim().substring(0, 50),
        amount: Math.max(0, Number(request.amount) || 0),
        timestamp: Number(request.timestamp) || Date.now(),
        signature: request.signature?.trim()
      };

      // 验证钱包地址格式
      if (!/^0x[a-fA-F0-9]{40}$/.test(sanitized.walletAddress)) {
        return null;
      }

      // 验证交易ID格式
      if (!/^[a-fA-F0-9]{64}$/.test(sanitized.transactionId)) {
        return null;
      }

      return sanitized;

    } catch (error) {
      console.error('输入清理失败:', error);
      return null;
    }
  }

  /**
   * 防重放攻击检查
   */
  private async checkReplayAttack(request: PaymentVerificationRequest): Promise<{ isValid: boolean; errors: string[] }> {
    try {
      const replayKey = `payment_replay:${request.transactionId}`;
      const existing = await redis.get(replayKey);
      
      if (existing) {
        return {
          isValid: false,
          errors: ['检测到重放攻击：交易ID已被使用']
        };
      }

      // 记录交易ID，防止重复使用（24小时过期）
      await redis.setex(replayKey, 86400, '1');

      return { isValid: true, errors: [] };

    } catch (error) {
      console.error('重放攻击检查失败:', error);
      return {
        isValid: false,
        errors: ['重放攻击检查失败']
      };
    }
  }

  /**
   * 速率限制检查
   */
  private async checkRateLimit(walletAddress: string): Promise<{ isValid: boolean; errors: string[] }> {
    try {
      const minuteKey = `rate_limit:${walletAddress}:${Math.floor(Date.now() / 60000)}`;
      const hourKey = `rate_limit:${walletAddress}:${Math.floor(Date.now() / 3600000)}`;

      const [minuteCount, hourCount] = await Promise.all([
        redis.incr(minuteKey),
        redis.incr(hourKey)
      ]);

      // 设置过期时间
      await Promise.all([
        redis.expire(minuteKey, 60),
        redis.expire(hourKey, 3600)
      ]);

      const errors: string[] = [];

      if (minuteCount > this.securityConfig.maxRequestsPerMinute) {
        errors.push('超过每分钟请求限制');
      }

      if (hourCount > this.securityConfig.maxRequestsPerHour) {
        errors.push('超过每小时请求限制');
      }

      return {
        isValid: errors.length === 0,
        errors
      };

    } catch (error) {
      console.error('速率限制检查失败:', error);
      return {
        isValid: false,
        errors: ['速率限制检查失败']
      };
    }
  }

  /**
   * 签名验证
   */
  private verifySignature(request: PaymentVerificationRequest): { isValid: boolean; errors: string[] } {
    try {
      if (!request.signature) {
        return {
          isValid: false,
          errors: ['缺少签名']
        };
      }

      // 构建待签名的数据
      const dataToSign = `${request.transactionId}:${request.walletAddress}:${request.productId}:${request.amount}:${request.timestamp}`;
      
      // 使用HMAC-SHA256验证签名
      const expectedSignature = crypto
        .createHmac('sha256', this.dappPortalSecret)
        .update(dataToSign)
        .digest('hex');

      const isValid = crypto.timingSafeEqual(
        Buffer.from(request.signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );

      return {
        isValid,
        errors: isValid ? [] : ['签名验证失败']
      };

    } catch (error) {
      console.error('签名验证失败:', error);
      return {
        isValid: false,
        errors: ['签名验证过程中发生错误']
      };
    }
  }

  /**
   * 时间戳验证
   */
  private verifyTimestamp(timestamp: number): { isValid: boolean; errors: string[] } {
    const now = Date.now();
    const diff = Math.abs(now - timestamp);

    if (diff > this.securityConfig.timestampToleranceMs) {
      return {
        isValid: false,
        errors: ['时间戳超出允许范围']
      };
    }

    return { isValid: true, errors: [] };
  }

  /**
   * 黑名单检查
   */
  private async checkBlacklist(walletAddress: string): Promise<{ isValid: boolean; errors: string[] }> {
    try {
      const blacklistKey = `blacklist:${walletAddress}`;
      const isBlacklisted = await redis.get(blacklistKey);

      if (isBlacklisted) {
        return {
          isValid: false,
          errors: ['钱包地址在黑名单中']
        };
      }

      return { isValid: true, errors: [] };

    } catch (error) {
      console.error('黑名单检查失败:', error);
      return {
        isValid: true, // 检查失败时默认通过
        errors: []
      };
    }
  }

  /**
   * 风险评估
   */
  private async assessRisk(request: PaymentVerificationRequest): Promise<{ score: number; errors: string[] }> {
    let riskScore = 0;
    const errors: string[] = [];

    try {
      // 检查交易频率
      const recentTransactions = await this.getRecentTransactionCount(request.walletAddress);
      if (recentTransactions > 10) {
        riskScore += 20;
        errors.push('交易频率异常');
      }

      // 检查金额异常
      if (request.amount > 10000) { // 假设10000是异常高的金额
        riskScore += 15;
        errors.push('交易金额异常');
      }

      // 检查新钱包
      const walletAge = await this.getWalletAge(request.walletAddress);
      if (walletAge < 86400000) { // 小于1天
        riskScore += 10;
        errors.push('新钱包风险');
      }

      return { score: riskScore, errors };

    } catch (error) {
      console.error('风险评估失败:', error);
      return { score: 0, errors: [] };
    }
  }

  /**
   * DappPortal API验证
   */
  private async verifyWithDappPortal(request: PaymentVerificationRequest): Promise<{ isValid: boolean; errors: string[] }> {
    try {
      // 检查缓存
      const cacheKey = `dapp_portal_verify:${request.transactionId}`;
      const cached = await advancedCacheService.get(cacheKey);
      if (cached) {
        return cached;
      }

      // 调用DappPortal API
      const response = await fetch(`${this.dappPortalBaseUrl}/verify-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.dappPortalApiKey}`,
          'X-API-Key': this.dappPortalApiKey
        },
        body: JSON.stringify({
          transactionId: request.transactionId,
          walletAddress: request.walletAddress,
          productId: request.productId,
          amount: request.amount
        }),
        timeout: 10000 // 10秒超时
      });

      if (!response.ok) {
        throw new Error(`DappPortal API错误: ${response.status}`);
      }

      const result = await response.json();
      const verificationResult = {
        isValid: result.success === true,
        errors: result.success ? [] : [result.message || 'DappPortal验证失败']
      };

      // 缓存结果
      await advancedCacheService.set(cacheKey, verificationResult, CACHE_STRATEGIES.TEMPORARY);

      return verificationResult;

    } catch (error) {
      console.error('DappPortal验证失败:', error);
      return {
        isValid: false,
        errors: ['DappPortal验证服务不可用']
      };
    }
  }

  /**
   * 记录验证尝试
   */
  private async logVerificationAttempt(
    request: PaymentVerificationRequest,
    errors: string[],
    riskScore: number
  ): Promise<void> {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        transactionId: request.transactionId,
        walletAddress: request.walletAddress,
        productId: request.productId,
        amount: request.amount,
        isValid: errors.length === 0,
        errors,
        riskScore,
        ip: 'unknown' // 在实际使用中应该从请求中获取
      };

      // 记录到Redis（用于审计）
      const logKey = `payment_log:${Date.now()}:${request.transactionId}`;
      await redis.setex(logKey, 2592000, JSON.stringify(logEntry)); // 保存30天

      // 如果是高风险或失败的验证，额外记录
      if (riskScore > 50 || errors.length > 0) {
        const alertKey = `payment_alert:${Date.now()}:${request.transactionId}`;
        await redis.setex(alertKey, 604800, JSON.stringify(logEntry)); // 保存7天
      }

    } catch (error) {
      console.error('记录验证尝试失败:', error);
    }
  }

  /**
   * 获取最近交易数量
   */
  private async getRecentTransactionCount(walletAddress: string): Promise<number> {
    try {
      const key = `recent_tx:${walletAddress}`;
      const count = await redis.get(key);
      return parseInt(count || '0');
    } catch (error) {
      return 0;
    }
  }

  /**
   * 获取钱包年龄
   */
  private async getWalletAge(walletAddress: string): Promise<number> {
    try {
      const key = `wallet_age:${walletAddress}`;
      const createdAt = await redis.get(key);
      if (createdAt) {
        return Date.now() - parseInt(createdAt);
      }
      return Date.now(); // 如果没有记录，假设是新钱包
    } catch (error) {
      return Date.now();
    }
  }

  /**
   * 创建错误结果
   */
  private createErrorResult(
    request: PaymentVerificationRequest,
    errors: string[],
    startTime: number
  ): PaymentVerificationResult {
    return {
      isValid: false,
      transactionId: request.transactionId || 'unknown',
      productId: request.productId || 'unknown',
      amount: request.amount || 0,
      walletAddress: request.walletAddress || 'unknown',
      verificationTime: Date.now() - startTime,
      errors,
      riskScore: 100
    };
  }

  /**
   * 添加钱包到黑名单
   */
  async addToBlacklist(walletAddress: string, reason: string): Promise<void> {
    try {
      const blacklistKey = `blacklist:${walletAddress}`;
      const blacklistData = {
        reason,
        addedAt: new Date().toISOString(),
        addedBy: 'system'
      };
      
      await redis.set(blacklistKey, JSON.stringify(blacklistData));
      console.log(`钱包 ${walletAddress} 已添加到黑名单: ${reason}`);
    } catch (error) {
      console.error('添加黑名单失败:', error);
    }
  }

  /**
   * 从黑名单移除钱包
   */
  async removeFromBlacklist(walletAddress: string): Promise<void> {
    try {
      const blacklistKey = `blacklist:${walletAddress}`;
      await redis.del(blacklistKey);
      console.log(`钱包 ${walletAddress} 已从黑名单移除`);
    } catch (error) {
      console.error('移除黑名单失败:', error);
    }
  }

  /**
   * 获取安全统计信息
   */
  async getSecurityStats(): Promise<any> {
    try {
      const now = Date.now();
      const hourAgo = now - 3600000;
      
      // 这里应该实现实际的统计查询
      return {
        totalVerifications: 0,
        successfulVerifications: 0,
        failedVerifications: 0,
        averageRiskScore: 0,
        blacklistedWallets: 0,
        timestamp: now
      };
    } catch (error) {
      console.error('获取安全统计失败:', error);
      return null;
    }
  }
}

export default new SecurePaymentService();
