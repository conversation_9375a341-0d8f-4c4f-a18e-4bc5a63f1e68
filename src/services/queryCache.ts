/**
 * 查询缓存服务
 * 用于缓存高频数据库查询结果，提升API响应性能
 */

import { redis } from '../config/redis';
import { UserWallet } from '../models/UserWallet';
import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { VipMembership } from '../models/VipMembership';
import { ActiveBooster } from '../models/ActiveBooster';

interface CacheOptions {
  ttl?: number; // 缓存过期时间（秒）
  prefix?: string; // 缓存键前缀
}

class QueryCacheService {
  private defaultTTL = 300; // 默认5分钟缓存
  private shortTTL = 60;    // 短期缓存1分钟
  private longTTL = 1800;   // 长期缓存30分钟

  /**
   * 生成缓存键
   */
  private generateCacheKey(prefix: string, ...params: (string | number)[]): string {
    return `wolf_fun:${prefix}:${params.join(':')}`;
  }

  /**
   * 获取缓存数据
   */
  private async getCache<T>(key: string): Promise<T | null> {
    try {
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 设置缓存数据
   */
  private async setCache(key: string, data: any, ttl: number): Promise<void> {
    try {
      await redis.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      console.error('设置缓存失败:', error);
    }
  }

  /**
   * 删除缓存
   */
  private async deleteCache(pattern: string): Promise<void> {
    try {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.error('删除缓存失败:', error);
    }
  }

  /**
   * 缓存用户钱包信息
   */
  async getUserWallet(walletId: number, useCache: boolean = true): Promise<UserWallet | null> {
    const cacheKey = this.generateCacheKey('user_wallet', walletId);
    
    if (useCache) {
      const cached = await this.getCache<UserWallet>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const userWallet = await UserWallet.findOne({ where: { id: walletId } });
    if (userWallet) {
      await this.setCache(cacheKey, userWallet.toJSON(), this.shortTTL);
    }
    
    return userWallet;
  }

  /**
   * 缓存用户农场区列表
   */
  async getUserFarmPlots(walletId: number, useCache: boolean = true): Promise<FarmPlot[]> {
    const cacheKey = this.generateCacheKey('farm_plots', walletId);
    
    if (useCache) {
      const cached = await this.getCache<FarmPlot[]>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const farmPlots = await FarmPlot.findAll({
      where: { walletId },
      order: [['plotNumber', 'ASC']]
    });
    
    if (farmPlots.length > 0) {
      await this.setCache(cacheKey, farmPlots.map(plot => plot.toJSON()), this.defaultTTL);
    }
    
    return farmPlots;
  }

  /**
   * 缓存用户已解锁农场区列表
   */
  async getUserUnlockedFarmPlots(walletId: number, useCache: boolean = true): Promise<FarmPlot[]> {
    const cacheKey = this.generateCacheKey('farm_plots_unlocked', walletId);
    
    if (useCache) {
      const cached = await this.getCache<FarmPlot[]>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      order: [['plotNumber', 'ASC']]
    });
    
    if (farmPlots.length > 0) {
      await this.setCache(cacheKey, farmPlots.map(plot => plot.toJSON()), this.defaultTTL);
    }
    
    return farmPlots;
  }

  /**
   * 缓存用户配送线信息
   */
  async getUserDeliveryLine(walletId: number, useCache: boolean = true): Promise<DeliveryLine | null> {
    const cacheKey = this.generateCacheKey('delivery_line', walletId);
    
    if (useCache) {
      const cached = await this.getCache<DeliveryLine>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const deliveryLine = await DeliveryLine.findOne({ where: { walletId } });
    if (deliveryLine) {
      await this.setCache(cacheKey, deliveryLine.toJSON(), this.defaultTTL);
    }
    
    return deliveryLine;
  }

  /**
   * 缓存VIP效果信息
   */
  async getVipEffects(walletId: number, useCache: boolean = true): Promise<any> {
    const cacheKey = this.generateCacheKey('vip_effects', walletId);
    
    if (useCache) {
      const cached = await this.getCache<any>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const vipMembership = await VipMembership.findOne({ where: { walletId } });
    
    let vipEffects;
    if (!vipMembership || !vipMembership.checkAndUpdateStatus()) {
      vipEffects = {
        isVip: false,
        deliverySpeedMultiplier: 1,
        blockPriceMultiplier: 1,
        productionSpeedMultiplier: 1
      };
    } else {
      vipEffects = {
        isVip: true,
        deliverySpeedMultiplier: 1.3, // VIP 30% 出货线速度加成
        blockPriceMultiplier: 1.2,    // VIP 20% 出货线价格加成
        productionSpeedMultiplier: 1.3 // VIP 30% 牧场区生产速度加成
      };
    }
    
    // VIP状态变化较少，可以使用较长的缓存时间
    await this.setCache(cacheKey, vipEffects, this.longTTL);
    
    return vipEffects;
  }

  /**
   * 缓存加速器效果信息
   */
  async getBoosterEffects(walletId: number, useCache: boolean = true): Promise<any> {
    const cacheKey = this.generateCacheKey('booster_effects', walletId);
    
    if (useCache) {
      const cached = await this.getCache<any>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const activeBooster = await ActiveBooster.findOne({
      where: { 
        walletId,
        expiresAt: { [require('sequelize').Op.gt]: new Date() }
      },
      order: [['expiresAt', 'DESC']]
    });
    
    let boosterEffects;
    if (!activeBooster) {
      boosterEffects = {
        hasBooster: false,
        speedMultiplier: 1,
        boosterType: null
      };
    } else {
      // 根据加速器类型设置倍率
      const speedMultiplier = activeBooster.productId === 'speed_boost_2x' ? 2 : 
                             activeBooster.productId === 'speed_boost_4x' ? 4 : 1;
      
      boosterEffects = {
        hasBooster: true,
        speedMultiplier,
        boosterType: activeBooster.productId,
        expiresAt: activeBooster.expiresAt
      };
    }
    
    // 加速器状态变化较频繁，使用较短的缓存时间
    await this.setCache(cacheKey, boosterEffects, this.shortTTL);
    
    return boosterEffects;
  }

  /**
   * 缓存排行榜数据
   */
  async getLeaderboard(limit: number = 100, useCache: boolean = true): Promise<any[]> {
    const cacheKey = this.generateCacheKey('leaderboard', limit);
    
    if (useCache) {
      const cached = await this.getCache<any[]>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const userWallets = await UserWallet.findAll({
      order: [['gem', 'DESC']],
      limit
    });

    const leaderboard = userWallets.map((wallet, index) => ({
      rank: index + 1,
      walletId: wallet.id,
      walletAddress: wallet.walletAddress,
      gems: wallet.gem,
      milk: wallet.milk
    }));
    
    // 排行榜数据变化相对较慢，可以使用较长的缓存时间
    await this.setCache(cacheKey, leaderboard, this.longTTL);
    
    return leaderboard;
  }

  /**
   * 清除用户相关的所有缓存
   */
  async clearUserCache(walletId: number): Promise<void> {
    const patterns = [
      this.generateCacheKey('user_wallet', walletId),
      this.generateCacheKey('farm_plots', walletId) + '*',
      this.generateCacheKey('delivery_line', walletId),
      this.generateCacheKey('vip_effects', walletId),
      this.generateCacheKey('booster_effects', walletId)
    ];

    for (const pattern of patterns) {
      await this.deleteCache(pattern);
    }
  }

  /**
   * 清除排行榜缓存
   */
  async clearLeaderboardCache(): Promise<void> {
    await this.deleteCache(this.generateCacheKey('leaderboard', '*'));
  }

  /**
   * 批量预热缓存
   */
  async warmupCache(walletIds: number[]): Promise<void> {
    console.log(`开始预热缓存，用户数量: ${walletIds.length}`);
    
    const promises = walletIds.map(async (walletId) => {
      try {
        // 预热用户基础数据
        await Promise.all([
          this.getUserWallet(walletId, false),
          this.getUserFarmPlots(walletId, false),
          this.getUserDeliveryLine(walletId, false),
          this.getVipEffects(walletId, false),
          this.getBoosterEffects(walletId, false)
        ]);
      } catch (error) {
        console.error(`预热用户 ${walletId} 缓存失败:`, error);
      }
    });

    await Promise.all(promises);
    console.log('缓存预热完成');
  }
}

export default new QueryCacheService();
