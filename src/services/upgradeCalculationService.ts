/**
 * 升级计算优化服务
 * 专门用于优化农场区和配送线的升级计算性能
 */

import BigNumber from 'bignumber.js';
import { FarmPlotCalculator, DeliveryLineCalculator, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';
import { redis } from '../config/redis';

interface UpgradePreview {
  canUpgrade: boolean;
  currentLevel: number;
  nextLevel: number;
  upgradeCost: number;
  improvements: {
    [key: string]: {
      current: number;
      next: number;
      growth: number;
      growthPercentage: number;
    };
  };
}

interface FarmPlotUpgradePreview extends UpgradePreview {
  improvements: {
    productionSpeed: {
      current: number;
      next: number;
      growth: number;
      growthPercentage: number;
    };
    milkProduction: {
      current: number;
      next: number;
      growth: number;
      growthPercentage: number;
    };
    barnCount: {
      current: number;
      next: number;
      growth: number;
      growthPercentage: number;
    };
  };
}

interface DeliveryLineUpgradePreview extends UpgradePreview {
  improvements: {
    deliverySpeed: {
      current: number;
      next: number;
      growth: number;
      growthPercentage: number;
    };
    blockUnit: {
      current: number;
      next: number;
      growth: number;
      growthPercentage: number;
    };
    blockPrice: {
      current: number;
      next: number;
      growth: number;
      growthPercentage: number;
    };
  };
}

class UpgradeCalculationService {
  private cachePrefix = 'wolf_fun:upgrade_calc:';
  private cacheTTL = 300; // 5分钟缓存

  /**
   * 生成缓存键
   */
  private getCacheKey(type: string, ...params: (string | number)[]): string {
    return `${this.cachePrefix}${type}:${params.join(':')}`;
  }

  /**
   * 获取缓存数据
   */
  private async getCache<T>(key: string): Promise<T | null> {
    try {
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('获取升级计算缓存失败:', error);
      return null;
    }
  }

  /**
   * 设置缓存数据
   */
  private async setCache(key: string, data: any): Promise<void> {
    try {
      await redis.setex(key, this.cacheTTL, JSON.stringify(data));
    } catch (error) {
      console.error('设置升级计算缓存失败:', error);
    }
  }

  /**
   * 计算农场区升级预览（优化版本）
   */
  async calculateFarmPlotUpgradePreview(
    plotData: {
      level: number;
      productionSpeed: number;
      milkProduction: number;
      barnCount: number;
      upgradeCost: number;
    },
    vipMultiplier: number = 1,
    useCache: boolean = true
  ): Promise<FarmPlotUpgradePreview> {
    const cacheKey = this.getCacheKey('farm_plot', plotData.level, plotData.productionSpeed, 
                                     plotData.milkProduction, plotData.upgradeCost, vipMultiplier);
    
    if (useCache) {
      const cached = await this.getCache<FarmPlotUpgradePreview>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const canUpgrade = plotData.level < 20;
    const nextLevel = plotData.level + 1;

    let improvements: FarmPlotUpgradePreview['improvements'];

    if (canUpgrade) {
      // 使用优化的计算方法
      const nextProductionSpeed = FarmPlotCalculator.calculateUpgradedSpeed(plotData.productionSpeed);
      const nextMilkProduction = FarmPlotCalculator.calculateUpgradedProduction(plotData.milkProduction);
      const nextBarnCount = plotData.barnCount + 1;
      const nextUpgradeCost = FarmPlotCalculator.calculateUpgradeCost(plotData.upgradeCost);

      // 应用VIP效果到速度
      const currentSpeedWithVip = plotData.productionSpeed / vipMultiplier;
      const nextSpeedWithVip = nextProductionSpeed / vipMultiplier;

      improvements = {
        productionSpeed: {
          current: formatToThreeDecimalsNumber(currentSpeedWithVip),
          next: formatToThreeDecimalsNumber(nextSpeedWithVip),
          growth: formatToThreeDecimalsNumber(currentSpeedWithVip - nextSpeedWithVip), // 速度减少是改善
          growthPercentage: formatToThreeDecimalsNumber(((currentSpeedWithVip - nextSpeedWithVip) / currentSpeedWithVip) * 100)
        },
        milkProduction: {
          current: formatToThreeDecimalsNumber(plotData.milkProduction),
          next: formatToThreeDecimalsNumber(nextMilkProduction),
          growth: formatToThreeDecimalsNumber(nextMilkProduction - plotData.milkProduction),
          growthPercentage: 50 // 固定50%增长
        },
        barnCount: {
          current: plotData.barnCount,
          next: nextBarnCount,
          growth: 1,
          growthPercentage: formatToThreeDecimalsNumber((1 / plotData.barnCount) * 100)
        }
      };
    } else {
      // 已达到最高等级
      improvements = {
        productionSpeed: {
          current: formatToThreeDecimalsNumber(plotData.productionSpeed / vipMultiplier),
          next: formatToThreeDecimalsNumber(plotData.productionSpeed / vipMultiplier),
          growth: 0,
          growthPercentage: 0
        },
        milkProduction: {
          current: formatToThreeDecimalsNumber(plotData.milkProduction),
          next: formatToThreeDecimalsNumber(plotData.milkProduction),
          growth: 0,
          growthPercentage: 0
        },
        barnCount: {
          current: plotData.barnCount,
          next: plotData.barnCount,
          growth: 0,
          growthPercentage: 0
        }
      };
    }

    const result: FarmPlotUpgradePreview = {
      canUpgrade,
      currentLevel: plotData.level,
      nextLevel,
      upgradeCost: plotData.upgradeCost,
      improvements
    };

    // 缓存结果
    if (useCache) {
      await this.setCache(cacheKey, result);
    }

    return result;
  }

  /**
   * 计算配送线升级预览（优化版本）
   */
  async calculateDeliveryLineUpgradePreview(
    lineData: {
      level: number;
      deliverySpeed: number;
      blockUnit: number;
      blockPrice: number;
      upgradeCost: number;
    },
    vipSpeedMultiplier: number = 1,
    vipPriceMultiplier: number = 1,
    boostMultiplier: number = 1,
    useCache: boolean = true
  ): Promise<DeliveryLineUpgradePreview> {
    const cacheKey = this.getCacheKey('delivery_line', lineData.level, lineData.deliverySpeed,
                                     lineData.blockUnit, lineData.blockPrice, lineData.upgradeCost,
                                     vipSpeedMultiplier, vipPriceMultiplier, boostMultiplier);
    
    if (useCache) {
      const cached = await this.getCache<DeliveryLineUpgradePreview>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const canUpgrade = lineData.level < 50; // 假设配送线最高50级
    const nextLevel = lineData.level + 1;

    let improvements: DeliveryLineUpgradePreview['improvements'];

    if (canUpgrade) {
      // 使用优化的计算方法
      const nextDeliverySpeed = DeliveryLineCalculator.calculateUpgradedSpeed(lineData.deliverySpeed);
      const nextBlockUnit = DeliveryLineCalculator.calculateUpgradedUnit(lineData.blockUnit);
      const nextBlockPrice = DeliveryLineCalculator.calculateUpgradedPrice(lineData.blockPrice);
      const nextUpgradeCost = DeliveryLineCalculator.calculateUpgradeCost(lineData.upgradeCost);

      // 应用各种效果
      const currentSpeedWithEffects = lineData.deliverySpeed / (vipSpeedMultiplier * boostMultiplier);
      const nextSpeedWithEffects = nextDeliverySpeed / (vipSpeedMultiplier * boostMultiplier);
      const currentPriceWithVip = lineData.blockPrice * vipPriceMultiplier;
      const nextPriceWithVip = nextBlockPrice * vipPriceMultiplier;

      improvements = {
        deliverySpeed: {
          current: formatToThreeDecimalsNumber(currentSpeedWithEffects),
          next: formatToThreeDecimalsNumber(nextSpeedWithEffects),
          growth: formatToThreeDecimalsNumber(currentSpeedWithEffects - nextSpeedWithEffects), // 速度减少是改善
          growthPercentage: 1 // 固定1%改善
        },
        blockUnit: {
          current: formatToThreeDecimalsNumber(lineData.blockUnit),
          next: formatToThreeDecimalsNumber(nextBlockUnit),
          growth: formatToThreeDecimalsNumber(nextBlockUnit - lineData.blockUnit),
          growthPercentage: 100 // 固定100%增长（2倍）
        },
        blockPrice: {
          current: formatToThreeDecimalsNumber(currentPriceWithVip),
          next: formatToThreeDecimalsNumber(nextPriceWithVip),
          growth: formatToThreeDecimalsNumber(nextPriceWithVip - currentPriceWithVip),
          growthPercentage: 100 // 固定100%增长（2倍）
        }
      };
    } else {
      // 已达到最高等级
      const currentSpeedWithEffects = lineData.deliverySpeed / (vipSpeedMultiplier * boostMultiplier);
      const currentPriceWithVip = lineData.blockPrice * vipPriceMultiplier;

      improvements = {
        deliverySpeed: {
          current: formatToThreeDecimalsNumber(currentSpeedWithEffects),
          next: formatToThreeDecimalsNumber(currentSpeedWithEffects),
          growth: 0,
          growthPercentage: 0
        },
        blockUnit: {
          current: formatToThreeDecimalsNumber(lineData.blockUnit),
          next: formatToThreeDecimalsNumber(lineData.blockUnit),
          growth: 0,
          growthPercentage: 0
        },
        blockPrice: {
          current: formatToThreeDecimalsNumber(currentPriceWithVip),
          next: formatToThreeDecimalsNumber(currentPriceWithVip),
          growth: 0,
          growthPercentage: 0
        }
      };
    }

    const result: DeliveryLineUpgradePreview = {
      canUpgrade,
      currentLevel: lineData.level,
      nextLevel,
      upgradeCost: lineData.upgradeCost,
      improvements
    };

    // 缓存结果
    if (useCache) {
      await this.setCache(cacheKey, result);
    }

    return result;
  }

  /**
   * 批量计算农场区升级预览
   */
  async batchCalculateFarmPlotUpgradePreviews(
    plotsData: Array<{
      id: number;
      level: number;
      productionSpeed: number;
      milkProduction: number;
      barnCount: number;
      upgradeCost: number;
    }>,
    vipMultiplier: number = 1
  ): Promise<Map<number, FarmPlotUpgradePreview>> {
    const results = new Map<number, FarmPlotUpgradePreview>();
    
    // 并行计算所有预览
    const promises = plotsData.map(async (plotData) => {
      const preview = await this.calculateFarmPlotUpgradePreview(plotData, vipMultiplier);
      return { id: plotData.id, preview };
    });

    const resolvedPromises = await Promise.all(promises);
    
    resolvedPromises.forEach(({ id, preview }) => {
      results.set(id, preview);
    });

    return results;
  }

  /**
   * 预计算常用升级数据
   */
  async precomputeUpgradeData(): Promise<void> {
    console.log('开始预计算升级数据...');
    const startTime = Date.now();

    // 预计算农场区1-20级的升级预览
    const farmPromises = [];
    for (let level = 1; level < 20; level++) {
      const baseSpeed = 5 / Math.pow(1.05, level - 1);
      const baseProduction = Math.pow(1.5, level - 1);
      const baseCost = 200 * Math.pow(1.5, level - 1);
      
      farmPromises.push(
        this.calculateFarmPlotUpgradePreview({
          level,
          productionSpeed: baseSpeed,
          milkProduction: baseProduction,
          barnCount: level,
          upgradeCost: baseCost
        }, 1, false) // 不使用缓存，直接预计算
      );
    }

    // 预计算配送线1-20级的升级预览
    const deliveryPromises = [];
    for (let level = 1; level < 20; level++) {
      const baseSpeed = 5 / Math.pow(1.01, level - 1);
      const baseUnit = 5 * Math.pow(2, level - 1);
      const basePrice = 5 * Math.pow(2, level - 1);
      const baseCost = 500 * Math.pow(2, level - 1);
      
      deliveryPromises.push(
        this.calculateDeliveryLineUpgradePreview({
          level,
          deliverySpeed: baseSpeed,
          blockUnit: baseUnit,
          blockPrice: basePrice,
          upgradeCost: baseCost
        }, 1, 1, 1, false) // 不使用缓存，直接预计算
      );
    }

    await Promise.all([...farmPromises, ...deliveryPromises]);

    const precomputeTime = Date.now() - startTime;
    console.log(`升级数据预计算完成，耗时: ${precomputeTime}ms`);
  }

  /**
   * 清除升级计算缓存
   */
  async clearUpgradeCache(): Promise<void> {
    try {
      const keys = await redis.keys(`${this.cachePrefix}*`);
      if (keys.length > 0) {
        await redis.del(...keys);
        console.log(`清除了 ${keys.length} 个升级计算缓存`);
      }
    } catch (error) {
      console.error('清除升级计算缓存失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(): Promise<any> {
    try {
      const keys = await redis.keys(`${this.cachePrefix}*`);
      return {
        totalCacheEntries: keys.length,
        cachePrefix: this.cachePrefix,
        cacheTTL: this.cacheTTL
      };
    } catch (error) {
      console.error('获取升级计算缓存统计失败:', error);
      return { totalCacheEntries: 0, error: error.message };
    }
  }
}

export default new UpgradeCalculationService();
