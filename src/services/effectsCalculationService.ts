/**
 * VIP和加速器效果计算优化服务
 * 专门用于优化VIP效果和速度提升的计算性能
 */

import { VipMembership } from '../models/VipMembership';
import { ActiveBooster } from '../models/ActiveBooster';
import { redis } from '../config/redis';
import { Op } from 'sequelize';

interface VipEffects {
  isVip: boolean;
  deliverySpeedMultiplier: number;
  blockPriceMultiplier: number;
  productionSpeedMultiplier: number;
}

interface BoosterEffects {
  hasBooster: boolean;
  speedMultiplier: number;
  boosterType: string | null;
  expiresAt?: Date;
}

interface CombinedEffects {
  vip: VipEffects;
  booster: BoosterEffects;
  combined: {
    totalDeliverySpeedMultiplier: number;
    totalProductionSpeedMultiplier: number;
    blockPriceMultiplier: number;
  };
}

interface BatchEffectsResult {
  effects: Map<number, CombinedEffects>;
  cacheHits: number;
  cacheMisses: number;
  totalTime: number;
}

class EffectsCalculationService {
  private cachePrefix = 'wolf_fun:effects:';
  private vipCacheTTL = 1800; // VIP状态缓存30分钟
  private boosterCacheTTL = 60; // 加速器状态缓存1分钟

  /**
   * 生成缓存键
   */
  private getCacheKey(type: string, walletId: number): string {
    return `${this.cachePrefix}${type}:${walletId}`;
  }

  /**
   * 获取缓存数据
   */
  private async getCache<T>(key: string): Promise<T | null> {
    try {
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('获取效果缓存失败:', error);
      return null;
    }
  }

  /**
   * 设置缓存数据
   */
  private async setCache(key: string, data: any, ttl: number): Promise<void> {
    try {
      await redis.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      console.error('设置效果缓存失败:', error);
    }
  }

  /**
   * 计算VIP效果（优化版本）
   */
  async calculateVipEffects(walletId: number, useCache: boolean = true): Promise<VipEffects> {
    const cacheKey = this.getCacheKey('vip', walletId);
    
    if (useCache) {
      const cached = await this.getCache<VipEffects>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      const vipMembership = await VipMembership.findOne({ where: { walletId } });
      
      let vipEffects: VipEffects;
      if (!vipMembership || !vipMembership.checkAndUpdateStatus()) {
        vipEffects = {
          isVip: false,
          deliverySpeedMultiplier: 1,
          blockPriceMultiplier: 1,
          productionSpeedMultiplier: 1
        };
      } else {
        vipEffects = {
          isVip: true,
          deliverySpeedMultiplier: 1.3, // VIP 30% 出货线速度加成
          blockPriceMultiplier: 1.2,    // VIP 20% 出货线价格加成
          productionSpeedMultiplier: 1.3 // VIP 30% 牧场区生产速度加成
        };
      }
      
      // 缓存结果
      if (useCache) {
        await this.setCache(cacheKey, vipEffects, this.vipCacheTTL);
      }
      
      return vipEffects;
    } catch (error) {
      console.error('计算VIP效果失败:', error);
      return {
        isVip: false,
        deliverySpeedMultiplier: 1,
        blockPriceMultiplier: 1,
        productionSpeedMultiplier: 1
      };
    }
  }

  /**
   * 计算加速器效果（优化版本）
   */
  async calculateBoosterEffects(walletId: number, useCache: boolean = true): Promise<BoosterEffects> {
    const cacheKey = this.getCacheKey('booster', walletId);
    
    if (useCache) {
      const cached = await this.getCache<BoosterEffects>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      const activeBooster = await ActiveBooster.findOne({
        where: { 
          walletId,
          endTime: { [Op.gt]: new Date() },
          status: 'active'
        },
        order: [['endTime', 'DESC']]
      });
      
      let boosterEffects: BoosterEffects;
      if (!activeBooster || !activeBooster.isActive()) {
        boosterEffects = {
          hasBooster: false,
          speedMultiplier: 1,
          boosterType: null
        };
      } else {
        // 根据加速器类型设置倍率
        let speedMultiplier = 1;
        if (activeBooster.type === 'speed_boost') {
          speedMultiplier = activeBooster.multiplier;
        }
        
        boosterEffects = {
          hasBooster: true,
          speedMultiplier,
          boosterType: activeBooster.type,
          expiresAt: activeBooster.endTime
        };
      }
      
      // 缓存结果
      if (useCache) {
        await this.setCache(cacheKey, boosterEffects, this.boosterCacheTTL);
      }
      
      return boosterEffects;
    } catch (error) {
      console.error('计算加速器效果失败:', error);
      return {
        hasBooster: false,
        speedMultiplier: 1,
        boosterType: null
      };
    }
  }

  /**
   * 计算组合效果
   */
  async calculateCombinedEffects(walletId: number, useCache: boolean = true): Promise<CombinedEffects> {
    const [vipEffects, boosterEffects] = await Promise.all([
      this.calculateVipEffects(walletId, useCache),
      this.calculateBoosterEffects(walletId, useCache)
    ]);

    return {
      vip: vipEffects,
      booster: boosterEffects,
      combined: {
        totalDeliverySpeedMultiplier: vipEffects.deliverySpeedMultiplier * boosterEffects.speedMultiplier,
        totalProductionSpeedMultiplier: vipEffects.productionSpeedMultiplier, // 生产速度只受VIP影响
        blockPriceMultiplier: vipEffects.blockPriceMultiplier // 价格只受VIP影响
      }
    };
  }

  /**
   * 批量计算多个用户的效果
   */
  async batchCalculateEffects(walletIds: number[], useCache: boolean = true): Promise<BatchEffectsResult> {
    const startTime = Date.now();
    const effects = new Map<number, CombinedEffects>();
    let cacheHits = 0;
    let cacheMisses = 0;

    // 并行计算所有用户的效果
    const promises = walletIds.map(async (walletId) => {
      try {
        const combinedEffects = await this.calculateCombinedEffects(walletId, useCache);
        
        // 统计缓存命中情况
        if (useCache) {
          const vipCached = await this.getCache(this.getCacheKey('vip', walletId));
          const boosterCached = await this.getCache(this.getCacheKey('booster', walletId));
          
          if (vipCached && boosterCached) {
            cacheHits++;
          } else {
            cacheMisses++;
          }
        }
        
        return { walletId, effects: combinedEffects };
      } catch (error) {
        console.error(`计算用户 ${walletId} 效果失败:`, error);
        return {
          walletId,
          effects: {
            vip: {
              isVip: false,
              deliverySpeedMultiplier: 1,
              blockPriceMultiplier: 1,
              productionSpeedMultiplier: 1
            },
            booster: {
              hasBooster: false,
              speedMultiplier: 1,
              boosterType: null
            },
            combined: {
              totalDeliverySpeedMultiplier: 1,
              totalProductionSpeedMultiplier: 1,
              blockPriceMultiplier: 1
            }
          }
        };
      }
    });

    const results = await Promise.all(promises);
    
    results.forEach(({ walletId, effects: userEffects }) => {
      effects.set(walletId, userEffects);
    });

    const totalTime = Date.now() - startTime;

    return {
      effects,
      cacheHits,
      cacheMisses,
      totalTime
    };
  }

  /**
   * 批量应用VIP效果到数值数组
   */
  batchApplyVipEffects(values: number[], vipMultiplier: number): number[] {
    return values.map(value => parseFloat((value * vipMultiplier).toFixed(3)));
  }

  /**
   * 批量应用速度效果到数值数组（速度是除法）
   */
  batchApplySpeedEffects(speeds: number[], speedMultiplier: number): number[] {
    return speeds.map(speed => parseFloat((speed / speedMultiplier).toFixed(3)));
  }

  /**
   * 预计算常用效果组合
   */
  async precomputeCommonEffects(): Promise<void> {
    console.log('开始预计算常用效果组合...');
    const startTime = Date.now();

    // 预计算VIP和非VIP的基础效果
    const commonEffects = [
      { isVip: false, hasBooster: false },
      { isVip: true, hasBooster: false },
      { isVip: false, hasBooster: true, speedMultiplier: 2 },
      { isVip: true, hasBooster: true, speedMultiplier: 2 },
      { isVip: false, hasBooster: true, speedMultiplier: 4 },
      { isVip: true, hasBooster: true, speedMultiplier: 4 }
    ];

    for (const effect of commonEffects) {
      const vipEffects: VipEffects = {
        isVip: effect.isVip,
        deliverySpeedMultiplier: effect.isVip ? 1.3 : 1,
        blockPriceMultiplier: effect.isVip ? 1.2 : 1,
        productionSpeedMultiplier: effect.isVip ? 1.3 : 1
      };

      const boosterEffects: BoosterEffects = {
        hasBooster: effect.hasBooster,
        speedMultiplier: effect.hasBooster ? (effect.speedMultiplier || 1) : 1,
        boosterType: effect.hasBooster ? 'speed_boost' : null
      };

      // 缓存预计算的效果
      const cacheKey = `${this.cachePrefix}precomputed:${effect.isVip ? 'vip' : 'novip'}_${effect.hasBooster ? `boost${effect.speedMultiplier || 1}` : 'noboost'}`;
      await this.setCache(cacheKey, { vip: vipEffects, booster: boosterEffects }, 3600);
    }

    const precomputeTime = Date.now() - startTime;
    console.log(`效果预计算完成，耗时: ${precomputeTime}ms`);
  }

  /**
   * 清除效果缓存
   */
  async clearEffectsCache(walletId?: number): Promise<void> {
    try {
      if (walletId) {
        // 清除特定用户的缓存
        const keys = [
          this.getCacheKey('vip', walletId),
          this.getCacheKey('booster', walletId)
        ];
        await redis.del(...keys);
      } else {
        // 清除所有效果缓存
        const keys = await redis.keys(`${this.cachePrefix}*`);
        if (keys.length > 0) {
          await redis.del(...keys);
        }
      }
    } catch (error) {
      console.error('清除效果缓存失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(): Promise<any> {
    try {
      const vipKeys = await redis.keys(`${this.cachePrefix}vip:*`);
      const boosterKeys = await redis.keys(`${this.cachePrefix}booster:*`);
      
      return {
        vipCacheEntries: vipKeys.length,
        boosterCacheEntries: boosterKeys.length,
        totalCacheEntries: vipKeys.length + boosterKeys.length,
        vipCacheTTL: this.vipCacheTTL,
        boosterCacheTTL: this.boosterCacheTTL
      };
    } catch (error) {
      console.error('获取效果缓存统计失败:', error);
      return { error: error.message };
    }
  }
}

export default new EffectsCalculationService();
