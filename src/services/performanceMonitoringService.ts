/**
 * 性能监控和测试验证服务
 * 实现全面的性能指标监控、压力测试、自动化测试、性能回归测试等功能
 */

import { EventEmitter } from 'events';
import performanceMonitor from '../utils/performanceMonitor';
import performanceTest from '../utils/performanceTest';
import { sequelize } from '../config/db';
import { redis } from '../config/redis';
import advancedCacheService from './advancedCacheService';
import concurrencyControlService from './concurrencyControlService';

interface PerformanceMetrics {
  timestamp: number;
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
  database: {
    activeConnections: number;
    queuedQueries: number;
    averageQueryTime: number;
    slowQueries: number;
  };
  cache: {
    hitRate: number;
    memoryUsage: number;
    operations: number;
  };
  api: {
    requestsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
  };
}

interface LoadTestConfig {
  duration: number; // 测试持续时间（秒）
  concurrentUsers: number; // 并发用户数
  rampUpTime: number; // 加压时间（秒）
  endpoints: Array<{
    path: string;
    method: string;
    weight: number; // 权重
    payload?: any;
  }>;
}

interface LoadTestResult {
  config: LoadTestConfig;
  startTime: number;
  endTime: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
  throughput: number;
  errors: Array<{
    endpoint: string;
    error: string;
    count: number;
  }>;
}

class PerformanceMonitoringService extends EventEmitter {
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private metricsHistory: PerformanceMetrics[] = [];
  private maxHistorySize = 1000;
  private alertThresholds = {
    cpuUsage: 80,
    memoryUsage: 85,
    dbResponseTime: 1000,
    cacheHitRate: 70,
    apiErrorRate: 5
  };

  /**
   * 启动性能监控
   */
  startMonitoring(intervalMs: number = 30000): void {
    if (this.isMonitoring) {
      console.log('性能监控已在运行');
      return;
    }

    this.isMonitoring = true;
    console.log('启动性能监控...');

    this.monitoringInterval = setInterval(async () => {
      try {
        const metrics = await this.collectMetrics();
        this.metricsHistory.push(metrics);
        
        // 限制历史记录大小
        if (this.metricsHistory.length > this.maxHistorySize) {
          this.metricsHistory.shift();
        }

        // 检查告警条件
        this.checkAlerts(metrics);

        // 发出监控事件
        this.emit('metrics', metrics);

      } catch (error) {
        console.error('收集性能指标失败:', error);
      }
    }, intervalMs);
  }

  /**
   * 停止性能监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('性能监控已停止');
  }

  /**
   * 收集性能指标
   */
  async collectMetrics(): Promise<PerformanceMetrics> {
    const timestamp = Date.now();

    // 收集系统指标
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const loadAverage = require('os').loadavg();

    // 收集数据库指标
    const dbStats = await this.collectDatabaseMetrics();
    
    // 收集缓存指标
    const cacheStats = await this.collectCacheMetrics();
    
    // 收集API指标
    const apiStats = await this.collectAPIMetrics();

    return {
      timestamp,
      cpu: {
        usage: this.calculateCPUUsage(cpuUsage),
        loadAverage
      },
      memory: {
        used: memUsage.rss,
        total: require('os').totalmem(),
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external
      },
      database: dbStats,
      cache: cacheStats,
      api: apiStats
    };
  }

  /**
   * 执行负载测试
   */
  async runLoadTest(config: LoadTestConfig): Promise<LoadTestResult> {
    console.log('开始负载测试...', config);
    
    const startTime = Date.now();
    const results: LoadTestResult = {
      config,
      startTime,
      endTime: 0,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      requestsPerSecond: 0,
      errorRate: 0,
      throughput: 0,
      errors: []
    };

    const responseTimes: number[] = [];
    const errors = new Map<string, number>();

    try {
      // 创建并发用户
      const userPromises: Promise<void>[] = [];
      
      for (let i = 0; i < config.concurrentUsers; i++) {
        const userPromise = this.simulateUser(config, results, responseTimes, errors);
        userPromises.push(userPromise);
        
        // 渐进式加压
        if (config.rampUpTime > 0) {
          const delay = (config.rampUpTime * 1000) / config.concurrentUsers;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      // 等待所有用户完成
      await Promise.all(userPromises);

      // 计算最终结果
      results.endTime = Date.now();
      const duration = (results.endTime - startTime) / 1000;
      
      results.averageResponseTime = responseTimes.length > 0 ? 
        responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;
      results.maxResponseTime = Math.max(...responseTimes, 0);
      results.minResponseTime = Math.min(...responseTimes, Infinity);
      results.requestsPerSecond = results.totalRequests / duration;
      results.errorRate = (results.failedRequests / results.totalRequests) * 100;
      results.throughput = results.successfulRequests / duration;

      // 转换错误统计
      results.errors = Array.from(errors.entries()).map(([error, count]) => ({
        endpoint: 'various',
        error,
        count
      }));

      console.log('负载测试完成:', results);
      return results;

    } catch (error) {
      console.error('负载测试失败:', error);
      throw error;
    }
  }

  /**
   * 执行性能回归测试
   */
  async runRegressionTest(): Promise<any> {
    console.log('开始性能回归测试...');
    
    const testResults = {
      timestamp: new Date(),
      tests: [] as any[],
      summary: {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        regressions: [] as any[]
      }
    };

    try {
      // 1. 数据库性能测试
      console.log('执行数据库性能测试...');
      const dbTest = await this.testDatabasePerformance();
      testResults.tests.push(dbTest);

      // 2. 缓存性能测试
      console.log('执行缓存性能测试...');
      const cacheTest = await this.testCachePerformance();
      testResults.tests.push(cacheTest);

      // 3. API性能测试
      console.log('执行API性能测试...');
      const apiTest = await this.testAPIPerformance();
      testResults.tests.push(apiTest);

      // 4. 并发控制测试
      console.log('执行并发控制测试...');
      const concurrencyTest = await this.testConcurrencyControl();
      testResults.tests.push(concurrencyTest);

      // 5. 内存泄漏测试
      console.log('执行内存泄漏测试...');
      const memoryTest = await this.testMemoryLeaks();
      testResults.tests.push(memoryTest);

      // 计算汇总结果
      testResults.summary.totalTests = testResults.tests.length;
      testResults.summary.passedTests = testResults.tests.filter(t => t.passed).length;
      testResults.summary.failedTests = testResults.tests.filter(t => !t.passed).length;
      testResults.summary.regressions = testResults.tests.filter(t => t.regression);

      console.log('性能回归测试完成:', testResults.summary);
      return testResults;

    } catch (error) {
      console.error('性能回归测试失败:', error);
      throw error;
    }
  }

  /**
   * 生成性能报告
   */
  async generatePerformanceReport(): Promise<any> {
    const report = {
      timestamp: new Date(),
      period: {
        start: this.metricsHistory.length > 0 ? new Date(this.metricsHistory[0].timestamp) : new Date(),
        end: new Date()
      },
      summary: {
        averageCPU: 0,
        averageMemory: 0,
        averageDBResponseTime: 0,
        averageCacheHitRate: 0,
        averageAPIResponseTime: 0,
        totalRequests: 0,
        errorRate: 0
      },
      trends: {
        cpu: [] as number[],
        memory: [] as number[],
        dbResponseTime: [] as number[],
        cacheHitRate: [] as number[],
        apiResponseTime: [] as number[]
      },
      alerts: [] as any[],
      recommendations: [] as string[]
    };

    if (this.metricsHistory.length === 0) {
      return report;
    }

    // 计算平均值
    const metrics = this.metricsHistory;
    report.summary.averageCPU = metrics.reduce((sum, m) => sum + m.cpu.usage, 0) / metrics.length;
    report.summary.averageMemory = metrics.reduce((sum, m) => sum + (m.memory.used / m.memory.total * 100), 0) / metrics.length;
    report.summary.averageDBResponseTime = metrics.reduce((sum, m) => sum + m.database.averageQueryTime, 0) / metrics.length;
    report.summary.averageCacheHitRate = metrics.reduce((sum, m) => sum + m.cache.hitRate, 0) / metrics.length;
    report.summary.averageAPIResponseTime = metrics.reduce((sum, m) => sum + m.api.averageResponseTime, 0) / metrics.length;

    // 生成趋势数据
    report.trends.cpu = metrics.map(m => m.cpu.usage);
    report.trends.memory = metrics.map(m => m.memory.used / m.memory.total * 100);
    report.trends.dbResponseTime = metrics.map(m => m.database.averageQueryTime);
    report.trends.cacheHitRate = metrics.map(m => m.cache.hitRate);
    report.trends.apiResponseTime = metrics.map(m => m.api.averageResponseTime);

    // 生成建议
    report.recommendations = this.generateRecommendations(report.summary);

    return report;
  }

  /**
   * 模拟用户行为
   */
  private async simulateUser(
    config: LoadTestConfig,
    results: LoadTestResult,
    responseTimes: number[],
    errors: Map<string, number>
  ): Promise<void> {
    const endTime = Date.now() + (config.duration * 1000);
    
    while (Date.now() < endTime) {
      try {
        // 随机选择端点
        const endpoint = this.selectRandomEndpoint(config.endpoints);
        const startTime = Date.now();
        
        // 模拟HTTP请求
        await this.simulateRequest(endpoint);
        
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
        
        results.totalRequests++;
        results.successfulRequests++;
        
        // 随机延迟
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
        
      } catch (error) {
        results.totalRequests++;
        results.failedRequests++;
        
        const errorKey = error instanceof Error ? error.message : 'Unknown error';
        errors.set(errorKey, (errors.get(errorKey) || 0) + 1);
      }
    }
  }

  /**
   * 随机选择端点
   */
  private selectRandomEndpoint(endpoints: LoadTestConfig['endpoints']): LoadTestConfig['endpoints'][0] {
    const totalWeight = endpoints.reduce((sum, ep) => sum + ep.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const endpoint of endpoints) {
      random -= endpoint.weight;
      if (random <= 0) {
        return endpoint;
      }
    }
    
    return endpoints[0];
  }

  /**
   * 模拟HTTP请求
   */
  private async simulateRequest(endpoint: LoadTestConfig['endpoints'][0]): Promise<void> {
    // 这里应该实际调用API端点
    // 为了演示，我们只是模拟延迟
    const delay = Math.random() * 200 + 50; // 50-250ms
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // 模拟一定的失败率
    if (Math.random() < 0.05) { // 5%失败率
      throw new Error('Simulated request failure');
    }
  }

  /**
   * 收集数据库指标
   */
  private async collectDatabaseMetrics(): Promise<PerformanceMetrics['database']> {
    try {
      const pool = sequelize.connectionManager.pool;
      const queryStats = performanceMonitor.getQueryStats();
      
      return {
        activeConnections: pool.using,
        queuedQueries: pool.waiting,
        averageQueryTime: queryStats.avgDuration,
        slowQueries: queryStats.slowQueries
      };
    } catch (error) {
      return {
        activeConnections: 0,
        queuedQueries: 0,
        averageQueryTime: 0,
        slowQueries: 0
      };
    }
  }

  /**
   * 收集缓存指标
   */
  private async collectCacheMetrics(): Promise<PerformanceMetrics['cache']> {
    try {
      const cacheStats = advancedCacheService.getStats();
      
      return {
        hitRate: cacheStats.hitRate,
        memoryUsage: 0, // 这里应该实现实际的内存使用统计
        operations: cacheStats.totalOperations
      };
    } catch (error) {
      return {
        hitRate: 0,
        memoryUsage: 0,
        operations: 0
      };
    }
  }

  /**
   * 收集API指标
   */
  private async collectAPIMetrics(): Promise<PerformanceMetrics['api']> {
    try {
      const apiStats = performanceMonitor.getAPIStats();
      
      return {
        requestsPerSecond: apiStats.totalRequests / 60, // 假设统计最近1分钟
        averageResponseTime: apiStats.avgDuration,
        errorRate: apiStats.errorRate
      };
    } catch (error) {
      return {
        requestsPerSecond: 0,
        averageResponseTime: 0,
        errorRate: 0
      };
    }
  }

  /**
   * 计算CPU使用率
   */
  private calculateCPUUsage(cpuUsage: NodeJS.CpuUsage): number {
    // 简化的CPU使用率计算
    return (cpuUsage.user + cpuUsage.system) / 1000000; // 转换为毫秒
  }

  /**
   * 检查告警条件
   */
  private checkAlerts(metrics: PerformanceMetrics): void {
    const alerts: any[] = [];

    // CPU使用率告警
    if (metrics.cpu.usage > this.alertThresholds.cpuUsage) {
      alerts.push({
        type: 'CPU_HIGH',
        message: `CPU使用率过高: ${metrics.cpu.usage.toFixed(2)}%`,
        threshold: this.alertThresholds.cpuUsage,
        current: metrics.cpu.usage
      });
    }

    // 内存使用率告警
    const memoryUsage = (metrics.memory.used / metrics.memory.total) * 100;
    if (memoryUsage > this.alertThresholds.memoryUsage) {
      alerts.push({
        type: 'MEMORY_HIGH',
        message: `内存使用率过高: ${memoryUsage.toFixed(2)}%`,
        threshold: this.alertThresholds.memoryUsage,
        current: memoryUsage
      });
    }

    // 数据库响应时间告警
    if (metrics.database.averageQueryTime > this.alertThresholds.dbResponseTime) {
      alerts.push({
        type: 'DB_SLOW',
        message: `数据库响应时间过长: ${metrics.database.averageQueryTime}ms`,
        threshold: this.alertThresholds.dbResponseTime,
        current: metrics.database.averageQueryTime
      });
    }

    // 缓存命中率告警
    if (metrics.cache.hitRate < this.alertThresholds.cacheHitRate) {
      alerts.push({
        type: 'CACHE_LOW_HIT_RATE',
        message: `缓存命中率过低: ${metrics.cache.hitRate.toFixed(2)}%`,
        threshold: this.alertThresholds.cacheHitRate,
        current: metrics.cache.hitRate
      });
    }

    // API错误率告警
    if (metrics.api.errorRate > this.alertThresholds.apiErrorRate) {
      alerts.push({
        type: 'API_HIGH_ERROR_RATE',
        message: `API错误率过高: ${metrics.api.errorRate.toFixed(2)}%`,
        threshold: this.alertThresholds.apiErrorRate,
        current: metrics.api.errorRate
      });
    }

    // 发出告警事件
    if (alerts.length > 0) {
      this.emit('alerts', alerts);
      console.warn('性能告警:', alerts);
    }
  }

  /**
   * 测试数据库性能
   */
  private async testDatabasePerformance(): Promise<any> {
    // 这里应该实现具体的数据库性能测试
    return {
      name: 'Database Performance Test',
      passed: true,
      regression: false,
      metrics: {
        queryTime: 100,
        throughput: 1000
      }
    };
  }

  /**
   * 测试缓存性能
   */
  private async testCachePerformance(): Promise<any> {
    // 这里应该实现具体的缓存性能测试
    return {
      name: 'Cache Performance Test',
      passed: true,
      regression: false,
      metrics: {
        hitRate: 85,
        responseTime: 5
      }
    };
  }

  /**
   * 测试API性能
   */
  private async testAPIPerformance(): Promise<any> {
    // 这里应该实现具体的API性能测试
    return {
      name: 'API Performance Test',
      passed: true,
      regression: false,
      metrics: {
        responseTime: 200,
        throughput: 500
      }
    };
  }

  /**
   * 测试并发控制
   */
  private async testConcurrencyControl(): Promise<any> {
    // 这里应该实现具体的并发控制测试
    return {
      name: 'Concurrency Control Test',
      passed: true,
      regression: false,
      metrics: {
        deadlocks: 0,
        lockWaitTime: 50
      }
    };
  }

  /**
   * 测试内存泄漏
   */
  private async testMemoryLeaks(): Promise<any> {
    // 这里应该实现具体的内存泄漏测试
    return {
      name: 'Memory Leak Test',
      passed: true,
      regression: false,
      metrics: {
        memoryGrowth: 0.1,
        gcFrequency: 10
      }
    };
  }

  /**
   * 生成性能建议
   */
  private generateRecommendations(summary: any): string[] {
    const recommendations: string[] = [];

    if (summary.averageCPU > 70) {
      recommendations.push('CPU使用率较高，建议优化计算密集型操作或增加服务器资源');
    }

    if (summary.averageMemory > 80) {
      recommendations.push('内存使用率较高，建议检查内存泄漏或优化内存使用');
    }

    if (summary.averageDBResponseTime > 500) {
      recommendations.push('数据库响应时间较长，建议优化查询或添加索引');
    }

    if (summary.averageCacheHitRate < 80) {
      recommendations.push('缓存命中率较低，建议优化缓存策略');
    }

    if (summary.averageAPIResponseTime > 1000) {
      recommendations.push('API响应时间较长，建议优化业务逻辑或使用缓存');
    }

    return recommendations;
  }

  /**
   * 获取监控状态
   */
  getMonitoringStatus(): any {
    return {
      isMonitoring: this.isMonitoring,
      metricsCount: this.metricsHistory.length,
      lastMetric: this.metricsHistory.length > 0 ? 
        this.metricsHistory[this.metricsHistory.length - 1] : null
    };
  }

  /**
   * 获取历史指标
   */
  getMetricsHistory(limit?: number): PerformanceMetrics[] {
    if (limit) {
      return this.metricsHistory.slice(-limit);
    }
    return [...this.metricsHistory];
  }

  /**
   * 清除历史指标
   */
  clearMetricsHistory(): void {
    this.metricsHistory = [];
  }
}

export default new PerformanceMonitoringService();
