/**
 * API响应优化服务
 * 专门用于优化API响应的数据结构和传输效率
 */

import { formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';

interface OptimizedResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    timestamp: number;
    version: string;
    cached?: boolean;
    processingTime?: number;
  };
}

interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

interface PaginatedResponse<T> extends OptimizedResponse<T[]> {
  pagination: PaginationMeta;
}

class APIResponseOptimizer {

  /**
   * 优化农场区响应数据
   */
  optimizeFarmPlotsResponse(farmPlots: any[], vipEffects: any, boosterEffects: any): OptimizedResponse<any[]> {
    const optimizedPlots = farmPlots.map(plot => {
      // 只返回前端需要的字段，减少数据传输量
      return {
        id: plot.id,
        plotNumber: plot.plotNumber,
        level: plot.level,
        isUnlocked: plot.isUnlocked,
        
        // 优化数值字段，确保3位小数精度
        productionSpeed: formatToThreeDecimalsNumber(plot.productionSpeed),
        milkProduction: formatToThreeDecimalsNumber(plot.milkProduction),
        accumulatedMilk: formatToThreeDecimalsNumber(plot.accumulatedMilk),
        upgradeCost: formatToThreeDecimalsNumber(plot.upgradeCost),
        unlockCost: plot.unlockCost ? formatToThreeDecimalsNumber(plot.unlockCost) : 0,
        
        barnCount: plot.barnCount,
        
        // 效果信息
        hasBoost: plot.hasBoost || false,
        boostMultiplier: plot.boostMultiplier || 1,
        
        // 升级预览数据
        nextUpgradeGrowth: plot.nextUpgradeGrowth ? {
          nextProductionSpeed: formatToThreeDecimalsNumber(plot.nextUpgradeGrowth.nextProductionSpeed),
          nextBarnCount: plot.nextUpgradeGrowth.nextBarnCount,
          nextMilkProduction: formatToThreeDecimalsNumber(plot.nextUpgradeGrowth.nextMilkProduction)
        } : null,
        
        // 时间戳（用于客户端缓存验证）
        lastUpdated: plot.updatedAt || new Date().toISOString()
      };
    });

    return {
      success: true,
      data: optimizedPlots,
      meta: {
        timestamp: Date.now(),
        version: '1.0',
        cached: false
      }
    };
  }

  /**
   * 优化配送线响应数据
   */
  optimizeDeliveryLineResponse(deliveryLine: any, vipEffects: any, boosterEffects: any): OptimizedResponse<any> {
    const optimizedLine = {
      id: deliveryLine.id,
      level: deliveryLine.level,
      
      // 优化数值字段
      deliverySpeed: formatToThreeDecimalsNumber(deliveryLine.deliverySpeed),
      blockUnit: formatToThreeDecimalsNumber(deliveryLine.blockUnit),
      blockPrice: formatToThreeDecimalsNumber(deliveryLine.blockPrice),
      upgradeCost: formatToThreeDecimalsNumber(deliveryLine.upgradeCost),
      
      // 效果信息
      hasBoost: deliveryLine.hasBoost || false,
      boostMultiplier: deliveryLine.boostMultiplier || 1,
      
      // 升级预览数据
      nextUpgradeGrowth: deliveryLine.nextUpgradeGrowth ? {
        nextDeliverySpeed: formatToThreeDecimalsNumber(deliveryLine.nextUpgradeGrowth.nextDeliverySpeed),
        nextBlockUnit: formatToThreeDecimalsNumber(deliveryLine.nextUpgradeGrowth.nextBlockUnit),
        nextBlockPrice: formatToThreeDecimalsNumber(deliveryLine.nextUpgradeGrowth.nextBlockPrice)
      } : null,
      
      // 时间戳
      lastUpdated: deliveryLine.updatedAt || new Date().toISOString()
    };

    return {
      success: true,
      data: optimizedLine,
      meta: {
        timestamp: Date.now(),
        version: '1.0',
        cached: false
      }
    };
  }

  /**
   * 优化用户钱包响应数据
   */
  optimizeUserWalletResponse(userWallet: any): OptimizedResponse<any> {
    const optimizedWallet = {
      id: userWallet.id,
      walletAddress: userWallet.walletAddress,
      
      // 优化数值字段
      gem: formatToThreeDecimalsNumber(userWallet.gem),
      milk: formatToThreeDecimalsNumber(userWallet.milk),
      
      // 时间信息
      lastActiveTime: userWallet.lastActiveTime,
      createdAt: userWallet.createdAt,
      
      // 移除敏感信息
      // 不返回内部ID、密钥等敏感数据
    };

    return {
      success: true,
      data: optimizedWallet,
      meta: {
        timestamp: Date.now(),
        version: '1.0',
        cached: false
      }
    };
  }

  /**
   * 优化离线收益响应数据
   */
  optimizeOfflineEarningsResponse(earnings: any): OptimizedResponse<any> {
    const optimizedEarnings = {
      // 优化数值字段
      milkEarned: formatToThreeDecimalsNumber(earnings.milkEarned),
      gemsEarned: formatToThreeDecimalsNumber(earnings.gemsEarned),
      offlineSeconds: earnings.offlineSeconds,
      
      // 详细收益信息
      farmEarnings: {
        totalMilk: formatToThreeDecimalsNumber(earnings.farmEarnings.totalMilk),
        plotCount: earnings.farmEarnings.plotEarnings.length,
        // 只返回汇总信息，不返回每个农场区的详细信息以减少数据量
      },
      
      deliveryEarnings: {
        totalGems: formatToThreeDecimalsNumber(earnings.deliveryEarnings.totalGems),
        blocksDelivered: earnings.deliveryEarnings.blocksDelivered,
        actualDeliverySpeed: formatToThreeDecimalsNumber(earnings.deliveryEarnings.actualDeliverySpeed),
        actualBlockPrice: formatToThreeDecimalsNumber(earnings.deliveryEarnings.actualBlockPrice)
      },
      
      // 统计信息
      stats: {
        offlineHours: formatToThreeDecimalsNumber(earnings.offlineSeconds / 3600),
        milkPerHour: earnings.offlineSeconds > 0 ? 
          formatToThreeDecimalsNumber(earnings.milkEarned / (earnings.offlineSeconds / 3600)) : 0,
        gemsPerHour: earnings.offlineSeconds > 0 ? 
          formatToThreeDecimalsNumber(earnings.gemsEarned / (earnings.offlineSeconds / 3600)) : 0
      }
    };

    return {
      success: true,
      data: optimizedEarnings,
      meta: {
        timestamp: Date.now(),
        version: '1.0',
        cached: false
      }
    };
  }

  /**
   * 优化排行榜响应数据
   */
  optimizeLeaderboardResponse(leaderboard: any[], page: number = 1, limit: number = 100): PaginatedResponse<any> {
    const optimizedLeaderboard = leaderboard.map((entry, index) => ({
      rank: entry.rank || (page - 1) * limit + index + 1,
      walletAddress: this.maskWalletAddress(entry.walletAddress),
      gems: formatToThreeDecimalsNumber(entry.gems),
      milk: formatToThreeDecimalsNumber(entry.milk),
      // 不返回完整的钱包地址以保护隐私
    }));

    const total = leaderboard.length;
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: optimizedLeaderboard,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      meta: {
        timestamp: Date.now(),
        version: '1.0',
        cached: true
      }
    };
  }

  /**
   * 优化错误响应
   */
  optimizeErrorResponse(error: Error, statusCode: number = 500): OptimizedResponse<null> {
    return {
      success: false,
      data: null,
      meta: {
        timestamp: Date.now(),
        version: '1.0',
        error: {
          code: statusCode,
          message: error.message,
          // 在生产环境中不返回堆栈跟踪
          ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
        }
      }
    };
  }

  /**
   * 批量优化响应数据
   */
  optimizeBatchResponse<T>(
    data: T[],
    optimizer: (item: T) => any,
    page?: number,
    limit?: number
  ): OptimizedResponse<any[]> | PaginatedResponse<any> {
    const optimizedData = data.map(optimizer);

    if (page && limit) {
      const total = optimizedData.length;
      const totalPages = Math.ceil(total / limit);
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedData = optimizedData.slice(startIndex, endIndex);

      return {
        success: true,
        data: paginatedData,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        },
        meta: {
          timestamp: Date.now(),
          version: '1.0',
          cached: false
        }
      };
    }

    return {
      success: true,
      data: optimizedData,
      meta: {
        timestamp: Date.now(),
        version: '1.0',
        cached: false
      }
    };
  }

  /**
   * 数据压缩优化
   * 移除null/undefined字段，减少传输数据量
   */
  compressResponseData(data: any): any {
    if (Array.isArray(data)) {
      return data.map(item => this.compressResponseData(item));
    }

    if (data && typeof data === 'object') {
      const compressed: any = {};
      
      for (const [key, value] of Object.entries(data)) {
        // 跳过null、undefined和空字符串
        if (value !== null && value !== undefined && value !== '') {
          compressed[key] = this.compressResponseData(value);
        }
      }
      
      return compressed;
    }

    return data;
  }

  /**
   * 掩码钱包地址以保护隐私
   */
  private maskWalletAddress(address: string): string {
    if (!address || address.length < 10) {
      return address;
    }
    
    const start = address.substring(0, 6);
    const end = address.substring(address.length - 4);
    return `${start}...${end}`;
  }

  /**
   * 添加响应元数据
   */
  addResponseMeta(data: any, options: {
    cached?: boolean;
    processingTime?: number;
    version?: string;
  } = {}): any {
    return {
      ...data,
      meta: {
        ...data.meta,
        timestamp: Date.now(),
        version: options.version || '1.0',
        cached: options.cached || false,
        processingTime: options.processingTime
      }
    };
  }

  /**
   * 验证响应数据完整性
   */
  validateResponseData(data: any): boolean {
    try {
      // 检查必要字段
      if (!data.hasOwnProperty('success')) {
        return false;
      }

      // 检查数据类型
      if (typeof data.success !== 'boolean') {
        return false;
      }

      // 检查元数据
      if (data.meta && typeof data.meta !== 'object') {
        return false;
      }

      return true;
    } catch (error) {
      console.error('响应数据验证失败:', error);
      return false;
    }
  }
}

export default new APIResponseOptimizer();
