/**
 * 并发控制和事务优化服务
 * 实现乐观锁、事务优化、连接池管理、死锁检测等功能
 */

import { sequelize } from '../config/db';
import { Transaction, Op } from 'sequelize';
import { redis } from '../config/redis';
import advancedCacheService from './advancedCacheService';

interface LockOptions {
  timeout: number;
  retryDelay: number;
  maxRetries: number;
}

interface TransactionOptions {
  isolationLevel?: string;
  timeout?: number;
  retryOnDeadlock?: boolean;
  maxRetries?: number;
}

interface ConcurrencyStats {
  activeTransactions: number;
  queuedTransactions: number;
  completedTransactions: number;
  failedTransactions: number;
  deadlockCount: number;
  averageTransactionTime: number;
}

class ConcurrencyControlService {
  private activeLocks = new Map<string, { timestamp: number; owner: string }>();
  private transactionQueue: Array<() => Promise<any>> = [];
  private maxConcurrentTransactions = 50;
  private currentTransactions = 0;
  private stats: ConcurrencyStats = {
    activeTransactions: 0,
    queuedTransactions: 0,
    completedTransactions: 0,
    failedTransactions: 0,
    deadlockCount: 0,
    averageTransactionTime: 0
  };

  /**
   * 乐观锁实现
   */
  async withOptimisticLock<T>(
    resourceId: string,
    operation: () => Promise<T>,
    options: LockOptions = { timeout: 10000, retryDelay: 100, maxRetries: 50 }
  ): Promise<T> {
    const lockKey = `optimistic_lock:${resourceId}`;
    const lockValue = `${Date.now()}-${Math.random()}`;
    
    for (let attempt = 0; attempt < options.maxRetries; attempt++) {
      try {
        // 尝试获取锁
        const acquired = await this.acquireOptimisticLock(lockKey, lockValue, options.timeout);
        
        if (acquired) {
          try {
            const result = await operation();
            await this.releaseOptimisticLock(lockKey, lockValue);
            return result;
          } catch (error) {
            await this.releaseOptimisticLock(lockKey, lockValue);
            throw error;
          }
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, options.retryDelay));
      } catch (error) {
        if (attempt === options.maxRetries - 1) {
          throw new Error(`获取乐观锁失败: ${error.message}`);
        }
      }
    }

    throw new Error('乐观锁获取超时');
  }

  /**
   * 优化的事务执行
   */
  async executeOptimizedTransaction<T>(
    operation: (transaction: Transaction) => Promise<T>,
    options: TransactionOptions = {}
  ): Promise<T> {
    const startTime = Date.now();
    
    // 检查并发限制
    if (this.currentTransactions >= this.maxConcurrentTransactions) {
      return new Promise((resolve, reject) => {
        this.transactionQueue.push(async () => {
          try {
            const result = await this.executeOptimizedTransaction(operation, options);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
        this.stats.queuedTransactions++;
      });
    }

    this.currentTransactions++;
    this.stats.activeTransactions++;

    let transaction: Transaction | null = null;
    let retryCount = 0;
    const maxRetries = options.maxRetries || 3;

    while (retryCount <= maxRetries) {
      try {
        // 创建事务
        transaction = await sequelize.transaction({
          isolationLevel: options.isolationLevel as any || Transaction.ISOLATION_LEVELS.READ_COMMITTED,
          timeout: options.timeout || 30000
        });

        // 执行操作
        const result = await operation(transaction);
        
        // 提交事务
        await transaction.commit();
        
        // 更新统计信息
        this.stats.completedTransactions++;
        this.stats.activeTransactions--;
        this.currentTransactions--;
        
        const duration = Date.now() - startTime;
        this.updateAverageTransactionTime(duration);
        
        // 处理队列中的下一个事务
        this.processTransactionQueue();
        
        return result;

      } catch (error) {
        if (transaction) {
          try {
            await transaction.rollback();
          } catch (rollbackError) {
            console.error('事务回滚失败:', rollbackError);
          }
        }

        // 检查是否是死锁错误
        if (this.isDeadlockError(error) && options.retryOnDeadlock !== false) {
          this.stats.deadlockCount++;
          retryCount++;
          
          if (retryCount <= maxRetries) {
            console.warn(`检测到死锁，第 ${retryCount} 次重试...`);
            await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 100));
            continue;
          }
        }

        // 更新统计信息
        this.stats.failedTransactions++;
        this.stats.activeTransactions--;
        this.currentTransactions--;
        
        // 处理队列中的下一个事务
        this.processTransactionQueue();
        
        throw error;
      }
    }

    throw new Error('事务执行失败，已达到最大重试次数');
  }

  /**
   * 批量事务优化
   */
  async executeBatchTransaction<T>(
    operations: Array<(transaction: Transaction) => Promise<T>>,
    batchSize: number = 10
  ): Promise<T[]> {
    const results: T[] = [];
    
    // 分批处理操作
    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize);
      
      const batchResult = await this.executeOptimizedTransaction(async (transaction) => {
        const batchResults: T[] = [];
        
        for (const operation of batch) {
          const result = await operation(transaction);
          batchResults.push(result);
        }
        
        return batchResults;
      });
      
      results.push(...batchResult);
    }
    
    return results;
  }

  /**
   * 连接池优化管理
   */
  async optimizeConnectionPool(): Promise<void> {
    try {
      const pool = sequelize.connectionManager.pool;
      const poolStats = {
        size: pool.size,
        available: pool.available,
        using: pool.using,
        waiting: pool.waiting
      };

      console.log('连接池状态:', poolStats);

      // 如果等待连接的请求过多，动态调整池大小
      if (poolStats.waiting > 10 && poolStats.size < 30) {
        console.log('动态增加连接池大小');
        // 这里可以实现动态调整逻辑
      }

      // 如果连接使用率很低，可以考虑减少连接数
      const utilizationRate = poolStats.using / poolStats.size;
      if (utilizationRate < 0.3 && poolStats.size > 10) {
        console.log('连接使用率较低，考虑优化');
      }

    } catch (error) {
      console.error('连接池优化失败:', error);
    }
  }

  /**
   * 死锁检测和处理
   */
  async detectAndHandleDeadlocks(): Promise<void> {
    try {
      // 查询当前的锁等待情况
      const lockWaits = await sequelize.query(`
        SELECT 
          r.trx_id waiting_trx_id,
          r.trx_mysql_thread_id waiting_thread,
          r.trx_query waiting_query,
          b.trx_id blocking_trx_id,
          b.trx_mysql_thread_id blocking_thread,
          b.trx_query blocking_query
        FROM information_schema.innodb_lock_waits w
        INNER JOIN information_schema.innodb_trx b ON b.trx_id = w.blocking_trx_id
        INNER JOIN information_schema.innodb_trx r ON r.trx_id = w.requesting_trx_id
      `, { type: sequelize.QueryTypes.SELECT });

      if (lockWaits.length > 0) {
        console.warn('检测到锁等待:', lockWaits);
        
        // 分析是否可能形成死锁
        const potentialDeadlocks = this.analyzePotentialDeadlocks(lockWaits);
        
        if (potentialDeadlocks.length > 0) {
          console.error('检测到潜在死锁:', potentialDeadlocks);
          // 这里可以实现死锁处理逻辑，比如终止某些事务
        }
      }

    } catch (error) {
      console.error('死锁检测失败:', error);
    }
  }

  /**
   * 分布式锁实现
   */
  async withDistributedLock<T>(
    lockKey: string,
    operation: () => Promise<T>,
    options: LockOptions = { timeout: 10000, retryDelay: 100, maxRetries: 50 }
  ): Promise<T> {
    const lockValue = await advancedCacheService.acquireDistributedLock(
      `distributed_lock:${lockKey}`,
      options
    );

    if (!lockValue) {
      throw new Error('获取分布式锁失败');
    }

    try {
      return await operation();
    } finally {
      await advancedCacheService.releaseDistributedLock(
        `distributed_lock:${lockKey}`,
        lockValue
      );
    }
  }

  /**
   * 事务重试机制
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // 检查是否应该重试
        if (attempt < maxRetries && this.shouldRetry(error)) {
          console.warn(`操作失败，第 ${attempt + 1} 次重试...`, error.message);
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
          continue;
        }
        
        throw error;
      }
    }

    throw lastError!;
  }

  /**
   * 获取乐观锁
   */
  private async acquireOptimisticLock(lockKey: string, lockValue: string, timeout: number): Promise<boolean> {
    try {
      const result = await redis.set(lockKey, lockValue, 'PX', timeout, 'NX');
      return result === 'OK';
    } catch (error) {
      console.error('获取乐观锁失败:', error);
      return false;
    }
  }

  /**
   * 释放乐观锁
   */
  private async releaseOptimisticLock(lockKey: string, lockValue: string): Promise<void> {
    try {
      const script = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `;
      
      await redis.eval(script, 1, lockKey, lockValue);
    } catch (error) {
      console.error('释放乐观锁失败:', error);
    }
  }

  /**
   * 检查是否是死锁错误
   */
  private isDeadlockError(error: any): boolean {
    const deadlockMessages = [
      'Deadlock found when trying to get lock',
      'Lock wait timeout exceeded',
      'ER_LOCK_DEADLOCK'
    ];
    
    const errorMessage = error.message || error.toString();
    return deadlockMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * 分析潜在死锁
   */
  private analyzePotentialDeadlocks(lockWaits: any[]): any[] {
    // 简化的死锁检测逻辑
    const waitGraph = new Map<string, string[]>();
    
    for (const wait of lockWaits) {
      const waiting = wait.waiting_trx_id;
      const blocking = wait.blocking_trx_id;
      
      if (!waitGraph.has(waiting)) {
        waitGraph.set(waiting, []);
      }
      waitGraph.get(waiting)!.push(blocking);
    }
    
    // 检查循环依赖
    const potentialDeadlocks: any[] = [];
    for (const [trxId, blockedBy] of waitGraph.entries()) {
      if (this.hasCycle(waitGraph, trxId, new Set())) {
        potentialDeadlocks.push({ transactionId: trxId, blockedBy });
      }
    }
    
    return potentialDeadlocks;
  }

  /**
   * 检查是否有循环依赖
   */
  private hasCycle(graph: Map<string, string[]>, node: string, visited: Set<string>): boolean {
    if (visited.has(node)) {
      return true;
    }
    
    visited.add(node);
    const neighbors = graph.get(node) || [];
    
    for (const neighbor of neighbors) {
      if (this.hasCycle(graph, neighbor, new Set(visited))) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: any): boolean {
    const retryableErrors = [
      'Connection lost',
      'Connection acquire timeout',
      'Lock wait timeout exceeded',
      'Deadlock found'
    ];
    
    const errorMessage = error.message || error.toString();
    return retryableErrors.some(msg => errorMessage.includes(msg));
  }

  /**
   * 处理事务队列
   */
  private processTransactionQueue(): void {
    if (this.transactionQueue.length > 0 && this.currentTransactions < this.maxConcurrentTransactions) {
      const nextTransaction = this.transactionQueue.shift();
      if (nextTransaction) {
        this.stats.queuedTransactions--;
        nextTransaction();
      }
    }
  }

  /**
   * 更新平均事务时间
   */
  private updateAverageTransactionTime(duration: number): void {
    const totalTransactions = this.stats.completedTransactions;
    this.stats.averageTransactionTime = 
      (this.stats.averageTransactionTime * (totalTransactions - 1) + duration) / totalTransactions;
  }

  /**
   * 获取并发控制统计信息
   */
  getStats(): ConcurrencyStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      activeTransactions: this.stats.activeTransactions, // 保持当前活跃事务数
      queuedTransactions: this.stats.queuedTransactions, // 保持当前队列数
      completedTransactions: 0,
      failedTransactions: 0,
      deadlockCount: 0,
      averageTransactionTime: 0
    };
  }

  /**
   * 启动定期优化任务
   */
  startOptimizationTasks(): void {
    // 每30秒检查连接池状态
    setInterval(() => {
      this.optimizeConnectionPool();
    }, 30000);

    // 每分钟检查死锁
    setInterval(() => {
      this.detectAndHandleDeadlocks();
    }, 60000);

    // 每5分钟清理过期锁
    setInterval(() => {
      this.cleanupExpiredLocks();
    }, 300000);
  }

  /**
   * 清理过期锁
   */
  private async cleanupExpiredLocks(): Promise<void> {
    try {
      const now = Date.now();
      for (const [lockKey, lockInfo] of this.activeLocks.entries()) {
        if (now - lockInfo.timestamp > 300000) { // 5分钟过期
          this.activeLocks.delete(lockKey);
          await redis.del(lockKey);
        }
      }
    } catch (error) {
      console.error('清理过期锁失败:', error);
    }
  }
}

export default new ConcurrencyControlService();
