/**
 * 离线收益计算优化服务
 * 专门用于优化离线收益计算的性能，支持VIP和加速器效果
 */

import BigNumber from 'bignumber.js';
import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { UserWallet } from '../models/UserWallet';
import { FarmPlotCalculator, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';
import effectsCalculationService from './effectsCalculationService';
import batchQueryService from './batchQueryService';

interface OfflineEarningsResult {
  milkEarned: number;
  gemsEarned: number;
  offlineSeconds: number;
  farmEarnings: {
    totalMilk: number;
    plotEarnings: Array<{
      plotId: number;
      milkProduced: number;
      cycles: number;
    }>;
  };
  deliveryEarnings: {
    totalGems: number;
    blocksDelivered: number;
    actualDeliverySpeed: number;
    actualBlockPrice: number;
  };
}

interface BatchOfflineEarningsResult {
  results: Map<number, OfflineEarningsResult>;
  totalTime: number;
  averageTime: number;
  effectsCalculationTime: number;
}

class OfflineEarningsService {

  /**
   * 计算单个用户的离线收益（优化版本）
   */
  async calculateUserOfflineEarnings(
    walletId: number,
    currentTime: Date = new Date()
  ): Promise<OfflineEarningsResult> {
    try {
      // 并行获取用户数据和效果
      const [gameData, effects] = await Promise.all([
        batchQueryService.batchGetUserGameData([walletId]),
        effectsCalculationService.calculateCombinedEffects(walletId)
      ]);

      const userData = gameData.get(walletId);
      if (!userData) {
        throw new Error('用户游戏数据不存在');
      }

      const { userWallet, farmPlots, deliveryLine } = userData;

      // 计算离线时间
      const lastActiveTime = userWallet.lastActiveTime || new Date();
      const offlineSeconds = Math.max(0, Math.floor((currentTime.getTime() - lastActiveTime.getTime()) / 1000));

      if (offlineSeconds === 0) {
        return this.createEmptyResult(offlineSeconds);
      }

      // 计算农场区离线收益
      const farmEarnings = this.calculateFarmOfflineEarnings(
        farmPlots,
        offlineSeconds,
        effects.combined.totalProductionSpeedMultiplier
      );

      // 计算配送线离线收益
      const deliveryEarnings = this.calculateDeliveryOfflineEarnings(
        deliveryLine,
        offlineSeconds,
        effects.combined.totalDeliverySpeedMultiplier,
        effects.combined.blockPriceMultiplier
      );

      return {
        milkEarned: farmEarnings.totalMilk,
        gemsEarned: deliveryEarnings.totalGems,
        offlineSeconds,
        farmEarnings,
        deliveryEarnings
      };

    } catch (error) {
      console.error(`计算用户 ${walletId} 离线收益失败:`, error);
      return this.createEmptyResult(0);
    }
  }

  /**
   * 批量计算多个用户的离线收益
   */
  async batchCalculateOfflineEarnings(
    walletIds: number[],
    currentTime: Date = new Date()
  ): Promise<BatchOfflineEarningsResult> {
    const startTime = Date.now();
    const results = new Map<number, OfflineEarningsResult>();

    // 并行获取所有用户的游戏数据和效果
    const effectsStartTime = Date.now();
    const [gameDataMap, effectsResult] = await Promise.all([
      batchQueryService.batchGetUserGameData(walletIds),
      effectsCalculationService.batchCalculateEffects(walletIds)
    ]);
    const effectsCalculationTime = Date.now() - effectsStartTime;

    // 并行计算所有用户的离线收益
    const promises = walletIds.map(async (walletId) => {
      try {
        const userData = gameDataMap.get(walletId);
        const userEffects = effectsResult.effects.get(walletId);

        if (!userData || !userEffects) {
          return { walletId, result: this.createEmptyResult(0) };
        }

        const { userWallet, farmPlots, deliveryLine } = userData;

        // 计算离线时间
        const lastActiveTime = userWallet.lastActiveTime || new Date();
        const offlineSeconds = Math.max(0, Math.floor((currentTime.getTime() - lastActiveTime.getTime()) / 1000));

        if (offlineSeconds === 0) {
          return { walletId, result: this.createEmptyResult(offlineSeconds) };
        }

        // 计算农场区离线收益
        const farmEarnings = this.calculateFarmOfflineEarnings(
          farmPlots,
          offlineSeconds,
          userEffects.combined.totalProductionSpeedMultiplier
        );

        // 计算配送线离线收益
        const deliveryEarnings = this.calculateDeliveryOfflineEarnings(
          deliveryLine,
          offlineSeconds,
          userEffects.combined.totalDeliverySpeedMultiplier,
          userEffects.combined.blockPriceMultiplier
        );

        const result: OfflineEarningsResult = {
          milkEarned: farmEarnings.totalMilk,
          gemsEarned: deliveryEarnings.totalGems,
          offlineSeconds,
          farmEarnings,
          deliveryEarnings
        };

        return { walletId, result };

      } catch (error) {
        console.error(`计算用户 ${walletId} 离线收益失败:`, error);
        return { walletId, result: this.createEmptyResult(0) };
      }
    });

    const resolvedPromises = await Promise.all(promises);
    
    resolvedPromises.forEach(({ walletId, result }) => {
      results.set(walletId, result);
    });

    const totalTime = Date.now() - startTime;

    return {
      results,
      totalTime,
      averageTime: totalTime / walletIds.length,
      effectsCalculationTime
    };
  }

  /**
   * 计算农场区离线收益
   */
  private calculateFarmOfflineEarnings(
    farmPlots: FarmPlot[],
    offlineSeconds: number,
    productionSpeedMultiplier: number
  ): OfflineEarningsResult['farmEarnings'] {
    let totalMilk = 0;
    const plotEarnings: Array<{ plotId: number; milkProduced: number; cycles: number }> = [];

    // 只计算已解锁的农场区
    const unlockedPlots = farmPlots.filter(plot => plot.isUnlocked);

    for (const plot of unlockedPlots) {
      try {
        // 计算实际生产速度（应用VIP效果）
        const actualProductionSpeed = plot.productionSpeed / productionSpeedMultiplier;
        
        // 计算生产周期数
        const cycles = Math.floor(offlineSeconds / actualProductionSpeed);
        
        if (cycles > 0) {
          // 计算基础产量
          const baseProduction = FarmPlotCalculator.calculateBaseProduction(plot.level);
          
          // 计算总产量 = 周期数 × 基础产量 × 牛舍数量
          const milkProduced = formatToThreeDecimalsNumber(
            new BigNumber(cycles)
              .multipliedBy(baseProduction)
              .multipliedBy(plot.barnCount)
          );
          
          totalMilk += milkProduced;
          
          plotEarnings.push({
            plotId: plot.id,
            milkProduced,
            cycles
          });
        }
      } catch (error) {
        console.error(`计算农场区 ${plot.id} 离线收益失败:`, error);
      }
    }

    return {
      totalMilk: formatToThreeDecimalsNumber(totalMilk),
      plotEarnings
    };
  }

  /**
   * 计算配送线离线收益
   */
  private calculateDeliveryOfflineEarnings(
    deliveryLine: DeliveryLine,
    offlineSeconds: number,
    deliverySpeedMultiplier: number,
    blockPriceMultiplier: number
  ): OfflineEarningsResult['deliveryEarnings'] {
    try {
      // 计算实际配送速度（应用VIP和加速器效果）
      const actualDeliverySpeed = deliveryLine.deliverySpeed / deliverySpeedMultiplier;
      
      // 计算实际方块价格（应用VIP效果）
      const actualBlockPrice = deliveryLine.blockPrice * blockPriceMultiplier;
      
      // 计算配送周期数
      const cycles = Math.floor(offlineSeconds / actualDeliverySpeed);
      
      // 计算可以配送的方块数量（不能超过待配送的方块数）
      const blocksDelivered = Math.min(cycles, deliveryLine.pendingBlocks);
      
      // 计算获得的宝石数量
      const totalGems = formatToThreeDecimalsNumber(
        new BigNumber(blocksDelivered).multipliedBy(actualBlockPrice)
      );

      return {
        totalGems,
        blocksDelivered,
        actualDeliverySpeed: formatToThreeDecimalsNumber(actualDeliverySpeed),
        actualBlockPrice: formatToThreeDecimalsNumber(actualBlockPrice)
      };

    } catch (error) {
      console.error('计算配送线离线收益失败:', error);
      return {
        totalGems: 0,
        blocksDelivered: 0,
        actualDeliverySpeed: deliveryLine.deliverySpeed,
        actualBlockPrice: deliveryLine.blockPrice
      };
    }
  }

  /**
   * 创建空的离线收益结果
   */
  private createEmptyResult(offlineSeconds: number): OfflineEarningsResult {
    return {
      milkEarned: 0,
      gemsEarned: 0,
      offlineSeconds,
      farmEarnings: {
        totalMilk: 0,
        plotEarnings: []
      },
      deliveryEarnings: {
        totalGems: 0,
        blocksDelivered: 0,
        actualDeliverySpeed: 0,
        actualBlockPrice: 0
      }
    };
  }

  /**
   * 应用离线收益到用户账户
   */
  async applyOfflineEarnings(
    walletId: number,
    earnings: OfflineEarningsResult
  ): Promise<void> {
    if (earnings.milkEarned === 0 && earnings.gemsEarned === 0) {
      return;
    }

    try {
      // 批量更新用户钱包余额
      await batchQueryService.batchUpdateUserWallets([{
        walletId,
        milkDelta: earnings.milkEarned,
        gemDelta: earnings.gemsEarned
      }]);

      // 更新配送线的待配送方块数量
      if (earnings.deliveryEarnings.blocksDelivered > 0) {
        const deliveryLine = await DeliveryLine.findOne({ where: { walletId } });
        if (deliveryLine) {
          deliveryLine.pendingBlocks -= earnings.deliveryEarnings.blocksDelivered;
          deliveryLine.lastDeliveryTime = new Date();
          await deliveryLine.save();
        }
      }

      // 更新用户最后活跃时间
      await UserWallet.update(
        { lastActiveTime: new Date() },
        { where: { id: walletId } }
      );

    } catch (error) {
      console.error(`应用用户 ${walletId} 离线收益失败:`, error);
      throw error;
    }
  }

  /**
   * 获取离线收益统计信息
   */
  getOfflineEarningsStats(earnings: OfflineEarningsResult): any {
    return {
      offlineHours: Math.round((earnings.offlineSeconds / 3600) * 100) / 100,
      milkPerHour: earnings.offlineSeconds > 0 ? 
        Math.round((earnings.milkEarned / (earnings.offlineSeconds / 3600)) * 100) / 100 : 0,
      gemsPerHour: earnings.offlineSeconds > 0 ? 
        Math.round((earnings.gemsEarned / (earnings.offlineSeconds / 3600)) * 100) / 100 : 0,
      activeFarmPlots: earnings.farmEarnings.plotEarnings.length,
      averageCyclesPerPlot: earnings.farmEarnings.plotEarnings.length > 0 ?
        Math.round(earnings.farmEarnings.plotEarnings.reduce((sum, plot) => sum + plot.cycles, 0) / earnings.farmEarnings.plotEarnings.length) : 0
    };
  }
}

  /**
   * 预计算离线收益表
   * 为常用的时间段和等级预计算收益数据
   */
  async precomputeOfflineEarningsTable(): Promise<void> {
    console.log('开始预计算离线收益表...');
    const startTime = Date.now();

    // 预计算常用的离线时间段（秒）
    const commonOfflineTimes = [
      300,    // 5分钟
      600,    // 10分钟
      1800,   // 30分钟
      3600,   // 1小时
      7200,   // 2小时
      14400,  // 4小时
      28800,  // 8小时
      43200,  // 12小时
      86400   // 24小时
    ];

    // 预计算常用的农场区等级
    const commonLevels = [1, 2, 3, 4, 5, 10, 15, 20];

    // 预计算常用的VIP和加速器组合
    const commonEffects = [
      { vip: false, boost: 1 },
      { vip: true, boost: 1 },
      { vip: false, boost: 2 },
      { vip: true, boost: 2 },
      { vip: false, boost: 4 },
      { vip: true, boost: 4 }
    ];

    let calculationCount = 0;

    for (const offlineTime of commonOfflineTimes) {
      for (const level of commonLevels) {
        for (const effect of commonEffects) {
          try {
            // 模拟农场区数据
            const mockFarmPlot = {
              id: 1,
              level,
              productionSpeed: 5 / Math.pow(1.05, level - 1),
              barnCount: level,
              isUnlocked: true
            } as FarmPlot;

            // 计算VIP效果
            const productionSpeedMultiplier = effect.vip ? 1.3 : 1;
            const deliverySpeedMultiplier = effect.vip ? 1.3 * effect.boost : effect.boost;
            const blockPriceMultiplier = effect.vip ? 1.2 : 1;

            // 计算农场收益
            this.calculateFarmOfflineEarnings(
              [mockFarmPlot],
              offlineTime,
              productionSpeedMultiplier
            );

            calculationCount++;
          } catch (error) {
            console.error('预计算离线收益失败:', error);
          }
        }
      }
    }

    const precomputeTime = Date.now() - startTime;
    console.log(`离线收益预计算完成，计算次数: ${calculationCount}，耗时: ${precomputeTime}ms`);
  }

  /**
   * 优化大数据量离线收益计算
   * 使用分段计算和内存优化
   */
  async calculateLargeScaleOfflineEarnings(
    walletIds: number[],
    batchSize: number = 100
  ): Promise<Map<number, OfflineEarningsResult>> {
    const results = new Map<number, OfflineEarningsResult>();

    console.log(`开始大规模离线收益计算，用户数量: ${walletIds.length}，批次大小: ${batchSize}`);

    // 分批处理
    for (let i = 0; i < walletIds.length; i += batchSize) {
      const batch = walletIds.slice(i, i + batchSize);
      console.log(`处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(walletIds.length / batchSize)}`);

      try {
        const batchResult = await this.batchCalculateOfflineEarnings(batch);

        // 合并结果
        batchResult.results.forEach((result, walletId) => {
          results.set(walletId, result);
        });

        // 强制垃圾回收（如果可用）
        if (global.gc) {
          global.gc();
        }

        // 短暂延迟以避免过载
        await new Promise(resolve => setTimeout(resolve, 10));

      } catch (error) {
        console.error(`批次 ${Math.floor(i / batchSize) + 1} 处理失败:`, error);
      }
    }

    console.log(`大规模离线收益计算完成，成功处理: ${results.size} 个用户`);
    return results;
  }

  /**
   * 内存优化的离线收益计算
   * 减少内存使用，适用于大量用户
   */
  async calculateOfflineEarningsMemoryOptimized(
    walletId: number,
    currentTime: Date = new Date()
  ): Promise<{ milkEarned: number; gemsEarned: number; offlineSeconds: number }> {
    try {
      // 只获取必要的数据，减少内存使用
      const [userWallet, farmPlots, deliveryLine, effects] = await Promise.all([
        UserWallet.findOne({
          where: { id: walletId },
          attributes: ['lastActiveTime'] // 只获取需要的字段
        }),
        FarmPlot.findAll({
          where: { walletId, isUnlocked: true },
          attributes: ['level', 'productionSpeed', 'barnCount'] // 只获取需要的字段
        }),
        DeliveryLine.findOne({
          where: { walletId },
          attributes: ['deliverySpeed', 'blockPrice', 'pendingBlocks'] // 只获取需要的字段
        }),
        effectsCalculationService.calculateCombinedEffects(walletId)
      ]);

      if (!userWallet || !deliveryLine) {
        return { milkEarned: 0, gemsEarned: 0, offlineSeconds: 0 };
      }

      // 计算离线时间
      const lastActiveTime = userWallet.lastActiveTime || new Date();
      const offlineSeconds = Math.max(0, Math.floor((currentTime.getTime() - lastActiveTime.getTime()) / 1000));

      if (offlineSeconds === 0) {
        return { milkEarned: 0, gemsEarned: 0, offlineSeconds: 0 };
      }

      // 简化的农场收益计算
      let totalMilk = 0;
      for (const plot of farmPlots) {
        const actualProductionSpeed = plot.productionSpeed / effects.combined.totalProductionSpeedMultiplier;
        const cycles = Math.floor(offlineSeconds / actualProductionSpeed);
        if (cycles > 0) {
          const baseProduction = FarmPlotCalculator.calculateBaseProduction(plot.level);
          totalMilk += cycles * baseProduction * plot.barnCount;
        }
      }

      // 简化的配送线收益计算
      const actualDeliverySpeed = deliveryLine.deliverySpeed / effects.combined.totalDeliverySpeedMultiplier;
      const actualBlockPrice = deliveryLine.blockPrice * effects.combined.blockPriceMultiplier;
      const cycles = Math.floor(offlineSeconds / actualDeliverySpeed);
      const blocksDelivered = Math.min(cycles, deliveryLine.pendingBlocks);
      const totalGems = blocksDelivered * actualBlockPrice;

      return {
        milkEarned: formatToThreeDecimalsNumber(totalMilk),
        gemsEarned: formatToThreeDecimalsNumber(totalGems),
        offlineSeconds
      };

    } catch (error) {
      console.error(`内存优化离线收益计算失败 (用户 ${walletId}):`, error);
      return { milkEarned: 0, gemsEarned: 0, offlineSeconds: 0 };
    }
  }
}

export default new OfflineEarningsService();
