/**
 * API优化中间件
 * 用于优化API响应时间，包括请求合并、并发处理、响应压缩等
 */

import { Request, Response, NextFunction } from 'express';
import compression from 'compression';
import { redis } from '../config/redis';
import advancedCacheService from '../services/advancedCacheService';

interface RequestBatch {
  requests: Array<{
    req: Request;
    res: Response;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }>;
  timer: NodeJS.Timeout;
}

interface OptimizedRequest extends Request {
  startTime?: number;
  batchKey?: string;
  skipBatching?: boolean;
}

class APIOptimizationService {
  private requestBatches = new Map<string, RequestBatch>();
  private batchTimeout = 50; // 50ms批处理窗口
  private maxBatchSize = 10; // 最大批处理大小

  /**
   * 请求合并中间件
   * 将相似的请求合并为批处理
   */
  requestBatchingMiddleware = (req: OptimizedRequest, res: Response, next: NextFunction): void => {
    // 只对特定的API端点启用批处理
    const batchableEndpoints = [
      '/api/farm/plots',
      '/api/delivery/line',
      '/api/user/wallet'
    ];

    const shouldBatch = batchableEndpoints.some(endpoint => req.path.includes(endpoint)) && 
                       req.method === 'GET' && 
                       !req.skipBatching;

    if (!shouldBatch) {
      return next();
    }

    // 生成批处理键（基于端点和查询参数）
    const batchKey = this.generateBatchKey(req);
    req.batchKey = batchKey;

    // 检查是否已有相同的批处理
    const existingBatch = this.requestBatches.get(batchKey);
    
    if (existingBatch && existingBatch.requests.length < this.maxBatchSize) {
      // 添加到现有批处理
      existingBatch.requests.push({
        req,
        res,
        resolve: (data) => res.json(data),
        reject: (error) => res.status(500).json({ error: error.message })
      });
    } else {
      // 创建新的批处理
      const newBatch: RequestBatch = {
        requests: [{
          req,
          res,
          resolve: (data) => res.json(data),
          reject: (error) => res.status(500).json({ error: error.message })
        }],
        timer: setTimeout(() => {
          this.processBatch(batchKey);
        }, this.batchTimeout)
      };
      
      this.requestBatches.set(batchKey, newBatch);
    }

    // 不调用next()，因为响应将在批处理中处理
  };

  /**
   * 并发控制中间件
   * 限制并发请求数量，防止系统过载
   */
  concurrencyControlMiddleware = (maxConcurrent: number = 100) => {
    let currentConcurrent = 0;
    const waitingQueue: Array<() => void> = [];

    return (req: Request, res: Response, next: NextFunction): void => {
      if (currentConcurrent < maxConcurrent) {
        currentConcurrent++;
        
        // 请求完成时减少计数
        res.on('finish', () => {
          currentConcurrent--;
          
          // 处理等待队列中的下一个请求
          if (waitingQueue.length > 0) {
            const nextRequest = waitingQueue.shift();
            if (nextRequest) {
              nextRequest();
            }
          }
        });
        
        next();
      } else {
        // 请求排队等待
        waitingQueue.push(() => {
          currentConcurrent++;
          
          res.on('finish', () => {
            currentConcurrent--;
            
            if (waitingQueue.length > 0) {
              const nextRequest = waitingQueue.shift();
              if (nextRequest) {
                nextRequest();
              }
            }
          });
          
          next();
        });
      }
    };
  };

  /**
   * 响应缓存中间件
   * 缓存GET请求的响应
   */
  responseCacheMiddleware = (ttl: number = 300) => {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      // 只缓存GET请求
      if (req.method !== 'GET') {
        return next();
      }

      const cacheKey = `api_cache:${req.originalUrl}`;
      
      try {
        // 尝试从缓存获取响应
        const cachedResponse = await advancedCacheService.get(cacheKey);
        if (cachedResponse) {
          return res.json(cachedResponse);
        }

        // 拦截响应
        const originalJson = res.json;
        res.json = function(data: any) {
          // 缓存响应数据
          advancedCacheService.set(cacheKey, data, { ttl });
          
          // 调用原始的json方法
          return originalJson.call(this, data);
        };

        next();
      } catch (error) {
        console.error('响应缓存中间件错误:', error);
        next();
      }
    };
  };

  /**
   * 数据预加载中间件
   * 预加载用户可能需要的数据
   */
  dataPreloadMiddleware = (req: Request, res: Response, next: NextFunction): void => {
    // 在后台预加载相关数据
    setImmediate(async () => {
      try {
        const walletId = this.extractWalletId(req);
        if (walletId) {
          await this.preloadUserData(walletId);
        }
      } catch (error) {
        console.error('数据预加载失败:', error);
      }
    });

    next();
  };

  /**
   * 响应压缩中间件配置
   */
  compressionMiddleware = compression({
    filter: (req, res) => {
      // 不压缩已经压缩的内容
      if (req.headers['x-no-compression']) {
        return false;
      }
      
      // 使用compression的默认过滤器
      return compression.filter(req, res);
    },
    level: 6, // 压缩级别 (1-9)
    threshold: 1024, // 只压缩大于1KB的响应
    memLevel: 8 // 内存使用级别
  });

  /**
   * 处理批处理请求
   */
  private async processBatch(batchKey: string): Promise<void> {
    const batch = this.requestBatches.get(batchKey);
    if (!batch) {
      return;
    }

    this.requestBatches.delete(batchKey);
    clearTimeout(batch.timer);

    try {
      // 根据批处理键确定处理方法
      if (batchKey.includes('farm/plots')) {
        await this.processFarmPlotsBatch(batch);
      } else if (batchKey.includes('delivery/line')) {
        await this.processDeliveryLineBatch(batch);
      } else if (batchKey.includes('user/wallet')) {
        await this.processUserWalletBatch(batch);
      } else {
        // 默认处理：逐个处理请求
        for (const { req, res, resolve, reject } of batch.requests) {
          try {
            // 这里需要调用实际的处理函数
            // 暂时返回空响应
            resolve({ message: 'Batch processed' });
          } catch (error) {
            reject(error);
          }
        }
      }
    } catch (error) {
      console.error('批处理失败:', error);
      
      // 所有请求都返回错误
      for (const { reject } of batch.requests) {
        reject(error);
      }
    }
  }

  /**
   * 处理农场区批处理
   */
  private async processFarmPlotsBatch(batch: RequestBatch): Promise<void> {
    const walletIds = batch.requests.map(({ req }) => this.extractWalletId(req)).filter(Boolean) as number[];
    
    // 这里应该调用批量获取农场区的服务
    // 暂时返回模拟数据
    const results = new Map();
    
    for (const { req, resolve } of batch.requests) {
      const walletId = this.extractWalletId(req);
      resolve({ walletId, farmPlots: [] });
    }
  }

  /**
   * 处理配送线批处理
   */
  private async processDeliveryLineBatch(batch: RequestBatch): Promise<void> {
    // 类似农场区的批处理逻辑
    for (const { req, resolve } of batch.requests) {
      const walletId = this.extractWalletId(req);
      resolve({ walletId, deliveryLine: {} });
    }
  }

  /**
   * 处理用户钱包批处理
   */
  private async processUserWalletBatch(batch: RequestBatch): Promise<void> {
    // 类似的批处理逻辑
    for (const { req, resolve } of batch.requests) {
      const walletId = this.extractWalletId(req);
      resolve({ walletId, wallet: {} });
    }
  }

  /**
   * 生成批处理键
   */
  private generateBatchKey(req: Request): string {
    const path = req.path;
    const query = JSON.stringify(req.query);
    return `${path}:${query}`;
  }

  /**
   * 从请求中提取钱包ID
   */
  private extractWalletId(req: Request): number | null {
    // 从路径参数、查询参数或请求体中提取钱包ID
    const walletId = req.params.walletId || req.query.walletId || req.body?.walletId;
    return walletId ? parseInt(walletId as string) : null;
  }

  /**
   * 预加载用户数据
   */
  private async preloadUserData(walletId: number): Promise<void> {
    try {
      // 预加载用户可能需要的数据
      const preloadPromises = [
        // 这里应该调用实际的服务方法
        // queryCache.getUserWallet(walletId),
        // queryCache.getUserFarmPlots(walletId),
        // queryCache.getUserDeliveryLine(walletId)
      ];

      await Promise.all(preloadPromises);
    } catch (error) {
      console.error('预加载用户数据失败:', error);
    }
  }

  /**
   * 获取API优化统计信息
   */
  getOptimizationStats(): any {
    return {
      activeBatches: this.requestBatches.size,
      batchTimeout: this.batchTimeout,
      maxBatchSize: this.maxBatchSize,
      timestamp: new Date()
    };
  }

  /**
   * 清理过期的批处理
   */
  cleanupExpiredBatches(): void {
    const now = Date.now();
    for (const [key, batch] of this.requestBatches.entries()) {
      // 如果批处理超过5秒还没处理，强制处理
      if (now - batch.timer.ref() > 5000) {
        this.processBatch(key);
      }
    }
  }
}

const apiOptimizationService = new APIOptimizationService();

// 启动定期清理任务
setInterval(() => {
  apiOptimizationService.cleanupExpiredBatches();
}, 10000); // 每10秒清理一次

export default apiOptimizationService;
