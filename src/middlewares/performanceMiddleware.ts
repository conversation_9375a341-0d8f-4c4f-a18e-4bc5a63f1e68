/**
 * 性能监控中间件
 * 用于监控API请求性能和数据库查询性能
 */

import { Request, Response, NextFunction } from 'express';
import performanceMonitor from '../utils/performanceMonitor';
import { sequelize } from '../config/db';

interface PerformanceRequest extends Request {
  startTime?: number;
  queryCount?: number;
  queryDuration?: number;
}

/**
 * API性能监控中间件
 */
export function apiPerformanceMiddleware(req: PerformanceRequest, res: Response, next: NextFunction): void {
  const startTime = Date.now();
  req.startTime = startTime;
  req.queryCount = 0;
  req.queryDuration = 0;

  // 监听响应结束事件
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const endpoint = req.route?.path || req.path;
    const method = req.method;
    const statusCode = res.statusCode;
    const userAgent = req.get('User-Agent');
    const ip = req.ip || req.connection.remoteAddress;

    // 记录API性能指标
    performanceMonitor.recordAPI(endpoint, method, duration, statusCode, userAgent, ip);

    // 记录详细日志（仅在开发环境或慢请求时）
    if (process.env.NODE_ENV === 'development' || duration > 1000) {
      console.log(`API性能: ${method} ${endpoint} - ${duration}ms (${statusCode}) - 查询次数: ${req.queryCount}, 查询时间: ${req.queryDuration}ms`);
    }
  });

  next();
}

/**
 * 数据库查询性能监控
 * 通过Sequelize的钩子函数监控所有数据库查询
 */
export function setupDatabasePerformanceMonitoring(): void {
  // 监控查询开始
  sequelize.addHook('beforeQuery', (options: any) => {
    options.startTime = Date.now();
  });

  // 监控查询结束
  sequelize.addHook('afterQuery', (options: any, query: any) => {
    const duration = Date.now() - options.startTime;
    const sql = options.sql || query?.sql || 'Unknown Query';
    
    // 记录查询性能
    performanceMonitor.recordQuery(sql, duration, true);

    // 在请求上下文中累计查询统计（如果存在）
    if (global.currentRequest) {
      const req = global.currentRequest as PerformanceRequest;
      if (req.queryCount !== undefined && req.queryDuration !== undefined) {
        req.queryCount++;
        req.queryDuration += duration;
      }
    }
  });

  // 监控查询错误
  sequelize.addHook('afterQueryError', (error: any, options: any) => {
    const duration = Date.now() - (options.startTime || Date.now());
    const sql = options.sql || 'Unknown Query';
    
    // 记录查询错误
    performanceMonitor.recordQuery(sql, duration, false, error.message);
  });
}

/**
 * 请求上下文中间件
 * 用于在数据库钩子中访问当前请求
 */
export function requestContextMiddleware(req: Request, res: Response, next: NextFunction): void {
  // 设置全局请求上下文
  global.currentRequest = req;

  // 清理上下文
  res.on('finish', () => {
    global.currentRequest = undefined;
  });

  next();
}

/**
 * 缓存性能监控装饰器
 */
export function monitorCacheOperation<T>(
  operation: 'get' | 'set' | 'del',
  key: string,
  fn: () => Promise<T>,
  isHit?: boolean
): Promise<T> {
  const startTime = Date.now();
  
  return fn().then(result => {
    const duration = Date.now() - startTime;
    const hit = isHit !== undefined ? isHit : (operation === 'get' && result !== null);
    
    performanceMonitor.recordCache(operation, key, hit, duration);
    
    return result;
  }).catch(error => {
    const duration = Date.now() - startTime;
    performanceMonitor.recordCache(operation, key, false, duration);
    throw error;
  });
}

/**
 * 性能报告中间件
 * 提供性能统计API端点
 */
export function performanceReportMiddleware(req: Request, res: Response): void {
  const timeRange = parseInt(req.query.timeRange as string) || 3600000; // 默认1小时

  performanceMonitor.generatePerformanceReport().then(report => {
    res.json({
      success: true,
      data: report
    });
  }).catch(error => {
    console.error('生成性能报告失败:', error);
    res.status(500).json({
      success: false,
      error: '生成性能报告失败'
    });
  });
}

/**
 * 健康检查中间件
 */
export async function healthCheckMiddleware(req: Request, res: Response): Promise<void> {
  try {
    const startTime = Date.now();
    
    // 检查数据库连接
    await sequelize.authenticate();
    const dbTime = Date.now() - startTime;
    
    // 检查Redis连接
    const redisStartTime = Date.now();
    const { redis } = require('../config/redis');
    await redis.ping();
    const redisTime = Date.now() - redisStartTime;
    
    // 获取系统状态
    const [poolStats, redisStats] = await Promise.all([
      performanceMonitor.getConnectionPoolStats(),
      performanceMonitor.getRedisStats()
    ]);

    const health = {
      status: 'healthy',
      timestamp: new Date(),
      services: {
        database: {
          status: 'healthy',
          responseTime: dbTime,
          connectionPool: poolStats
        },
        redis: {
          status: 'healthy',
          responseTime: redisTime,
          stats: redisStats
        }
      },
      performance: {
        queries: performanceMonitor.getQueryStats(300000), // 5分钟
        api: performanceMonitor.getAPIStats(300000),
        cache: performanceMonitor.getCacheStats(300000)
      }
    };

    // 检查是否有性能问题
    if (dbTime > 1000 || redisTime > 500) {
      health.status = 'degraded';
    }

    res.json(health);

  } catch (error) {
    console.error('健康检查失败:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date(),
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

/**
 * 性能警报检查
 * 定期检查性能指标并发出警报
 */
export function startPerformanceAlerts(): void {
  setInterval(async () => {
    try {
      const report = await performanceMonitor.generatePerformanceReport();
      
      // 检查关键性能指标
      const alerts: string[] = [];
      
      if (report.database.queries.avgDuration > 1000) {
        alerts.push(`数据库查询平均响应时间过高: ${report.database.queries.avgDuration}ms`);
      }
      
      if (report.database.queries.errorRate > 10) {
        alerts.push(`数据库查询错误率过高: ${report.database.queries.errorRate}%`);
      }
      
      if (report.api.avgDuration > 2000) {
        alerts.push(`API平均响应时间过高: ${report.api.avgDuration}ms`);
      }
      
      if (report.cache.performance.hitRate < 70) {
        alerts.push(`缓存命中率过低: ${report.cache.performance.hitRate}%`);
      }

      // 检查连接池使用率
      if (report.database.connectionPool) {
        const poolUsage = (report.database.connectionPool.using / report.database.connectionPool.maxConnections) * 100;
        if (poolUsage > 80) {
          alerts.push(`数据库连接池使用率过高: ${poolUsage.toFixed(1)}%`);
        }
      }
      
      // 发送警报
      if (alerts.length > 0) {
        console.warn('性能警报:', alerts);
        // 这里可以集成邮件、短信或其他通知服务
      }
      
    } catch (error) {
      console.error('性能警报检查失败:', error);
    }
  }, 60000); // 每分钟检查一次
}

// 声明全局类型
declare global {
  var currentRequest: Request | undefined;
}

export default {
  apiPerformanceMiddleware,
  setupDatabasePerformanceMonitoring,
  requestContextMiddleware,
  monitorCacheOperation,
  performanceReportMiddleware,
  healthCheckMiddleware,
  startPerformanceAlerts
};
