/**
 * 性能验证测试套件
 * 验证所有性能优化功能的正确性和效果
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/testing-framework';
import performanceMonitoringService from '../services/performanceMonitoringService';
import concurrencyControlService from '../services/concurrencyControlService';
import advancedCacheService from '../services/advancedCacheService';
import effectsCalculationService from '../services/effectsCalculationService';
import upgradeCalculationService from '../services/upgradeCalculationService';
import offlineEarningsService from '../services/offlineEarningsService';
import securePaymentService from '../services/securePaymentService';
import batchQueryService from '../services/batchQueryService';
import queryCache from '../services/queryCache';
import { FarmPlotCalculator, DeliveryLineCalculator } from '../utils/bigNumberConfig';
import batchCalculationService from '../utils/batchCalculationService';
import OptimizedBigNumberOps from '../utils/bigNumberPool';

describe('Wolf Fun 性能优化验证测试', () => {
  
  beforeAll(async () => {
    console.log('🚀 开始性能优化验证测试...');
    // 初始化测试环境
    await setupTestEnvironment();
  });

  afterAll(async () => {
    console.log('🏁 性能优化验证测试完成');
    // 清理测试环境
    await cleanupTestEnvironment();
  });

  describe('数据库查询和索引优化验证', () => {
    test('应该显著提升查询性能', async () => {
      const startTime = Date.now();
      
      // 模拟高频查询
      const promises = Array.from({ length: 100 }, (_, i) => 
        queryCache.getUserWallet(i + 1)
      );
      
      await Promise.all(promises);
      const duration = Date.now() - startTime;
      
      // 验证查询时间应该在合理范围内
      expect(duration).toBeLessThan(5000); // 100个查询应在5秒内完成
      console.log(`✓ 批量查询性能: ${duration}ms`);
    });

    test('应该正确使用缓存', async () => {
      const walletId = 1;
      
      // 第一次查询（缓存未命中）
      const start1 = Date.now();
      await queryCache.getUserWallet(walletId);
      const time1 = Date.now() - start1;
      
      // 第二次查询（缓存命中）
      const start2 = Date.now();
      await queryCache.getUserWallet(walletId);
      const time2 = Date.now() - start2;
      
      // 缓存命中应该显著更快
      expect(time2).toBeLessThan(time1 * 0.5);
      console.log(`✓ 缓存效果: 首次${time1}ms, 缓存${time2}ms`);
    });
  });

  describe('BigNumber.js计算性能优化验证', () => {
    test('应该提升计算性能', async () => {
      const iterations = 1000;
      
      // 测试优化后的计算性能
      const startTime = Date.now();
      for (let i = 0; i < iterations; i++) {
        FarmPlotCalculator.calculateBaseProduction(i % 20 + 1);
        FarmPlotCalculator.calculateUpgradedSpeed(5 - (i % 10) * 0.1);
        DeliveryLineCalculator.calculateUpgradedUnit(5 + (i % 5) * 2);
      }
      const duration = Date.now() - startTime;
      
      // 验证计算性能
      expect(duration).toBeLessThan(1000); // 1000次计算应在1秒内完成
      console.log(`✓ BigNumber计算性能: ${iterations}次计算耗时${duration}ms`);
    });

    test('应该正确使用对象池', async () => {
      const values = Array.from({ length: 100 }, () => Math.random() * 1000);
      
      const startTime = Date.now();
      const results = OptimizedBigNumberOps.batchMultiply(values, 1.5);
      const duration = Date.now() - startTime;
      
      expect(results).toHaveLength(100);
      expect(duration).toBeLessThan(100); // 批量计算应该很快
      console.log(`✓ 对象池批量计算: ${duration}ms`);
    });
  });

  describe('升级计算优化验证', () => {
    test('应该正确计算农场区升级预览', async () => {
      const plotData = {
        level: 5,
        productionSpeed: 4.535,
        milkProduction: 6.25,
        barnCount: 5,
        upgradeCost: 1687.5
      };

      const startTime = Date.now();
      const preview = await upgradeCalculationService.calculateFarmPlotUpgradePreview(plotData, 1.3);
      const duration = Date.now() - startTime;

      expect(preview.canUpgrade).toBe(true);
      expect(preview.improvements.productionSpeed.next).toBeDefined();
      expect(preview.improvements.milkProduction.next).toBeDefined();
      expect(preview.improvements.barnCount.next).toBe(6);
      expect(duration).toBeLessThan(100); // 单次计算应该很快
      
      console.log(`✓ 农场区升级预览计算: ${duration}ms`);
    });

    test('应该正确计算配送线升级预览', async () => {
      const lineData = {
        level: 3,
        deliverySpeed: 4.901,
        blockUnit: 20,
        blockPrice: 20,
        upgradeCost: 2000
      };

      const startTime = Date.now();
      const preview = await upgradeCalculationService.calculateDeliveryLineUpgradePreview(
        lineData, 1.3, 1.2, 2
      );
      const duration = Date.now() - startTime;

      expect(preview.canUpgrade).toBe(true);
      expect(preview.improvements.deliverySpeed.next).toBeDefined();
      expect(preview.improvements.blockUnit.next).toBeDefined();
      expect(preview.improvements.blockPrice.next).toBeDefined();
      expect(duration).toBeLessThan(100);
      
      console.log(`✓ 配送线升级预览计算: ${duration}ms`);
    });
  });

  describe('VIP效果和速度提升优化验证', () => {
    test('应该正确计算VIP效果', async () => {
      const walletId = 1;
      
      const startTime = Date.now();
      const vipEffects = await effectsCalculationService.calculateVipEffects(walletId);
      const duration = Date.now() - startTime;

      expect(vipEffects).toHaveProperty('isVip');
      expect(vipEffects).toHaveProperty('deliverySpeedMultiplier');
      expect(vipEffects).toHaveProperty('blockPriceMultiplier');
      expect(vipEffects).toHaveProperty('productionSpeedMultiplier');
      expect(duration).toBeLessThan(50);
      
      console.log(`✓ VIP效果计算: ${duration}ms`);
    });

    test('应该正确批量计算效果', async () => {
      const walletIds = Array.from({ length: 50 }, (_, i) => i + 1);
      
      const startTime = Date.now();
      const batchResult = await effectsCalculationService.batchCalculateEffects(walletIds);
      const duration = Date.now() - startTime;

      expect(batchResult.effects.size).toBe(50);
      expect(duration).toBeLessThan(2000); // 50个用户的效果计算应在2秒内完成
      
      console.log(`✓ 批量效果计算: ${duration}ms, 缓存命中率: ${(batchResult.cacheHits / (batchResult.cacheHits + batchResult.cacheMisses) * 100).toFixed(2)}%`);
    });
  });

  describe('离线收益计算优化验证', () => {
    test('应该正确计算离线收益', async () => {
      const walletId = 1;
      const currentTime = new Date();
      
      const startTime = Date.now();
      const earnings = await offlineEarningsService.calculateUserOfflineEarnings(walletId, currentTime);
      const duration = Date.now() - startTime;

      expect(earnings).toHaveProperty('milkEarned');
      expect(earnings).toHaveProperty('gemsEarned');
      expect(earnings).toHaveProperty('offlineSeconds');
      expect(earnings).toHaveProperty('farmEarnings');
      expect(earnings).toHaveProperty('deliveryEarnings');
      expect(duration).toBeLessThan(500);
      
      console.log(`✓ 离线收益计算: ${duration}ms`);
    });

    test('应该正确批量计算离线收益', async () => {
      const walletIds = Array.from({ length: 20 }, (_, i) => i + 1);
      
      const startTime = Date.now();
      const batchResult = await offlineEarningsService.batchCalculateOfflineEarnings(walletIds);
      const duration = Date.now() - startTime;

      expect(batchResult.results.size).toBe(20);
      expect(duration).toBeLessThan(3000); // 20个用户的离线收益应在3秒内完成
      
      console.log(`✓ 批量离线收益计算: ${duration}ms, 平均每用户: ${batchResult.averageTime.toFixed(2)}ms`);
    });
  });

  describe('Redis缓存策略优化验证', () => {
    test('应该正确实现多层缓存', async () => {
      const key = 'test_cache_key';
      const data = { test: 'data', timestamp: Date.now() };
      
      // 设置缓存
      const setResult = await advancedCacheService.set(key, data, { ttl: 300 });
      expect(setResult).toBe(true);
      
      // 获取缓存（应该命中Redis）
      const start1 = Date.now();
      const cached1 = await advancedCacheService.get(key);
      const time1 = Date.now() - start1;
      
      // 再次获取（应该命中本地缓存）
      const start2 = Date.now();
      const cached2 = await advancedCacheService.get(key);
      const time2 = Date.now() - start2;
      
      expect(cached1).toEqual(data);
      expect(cached2).toEqual(data);
      expect(time2).toBeLessThan(time1); // 本地缓存应该更快
      
      console.log(`✓ 多层缓存: Redis ${time1}ms, 本地 ${time2}ms`);
    });

    test('应该正确实现分布式锁', async () => {
      const lockKey = 'test_lock';
      let lockAcquired = false;
      
      const result = await advancedCacheService.withDistributedLock(lockKey, async () => {
        lockAcquired = true;
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'success';
      });
      
      expect(result).toBe('success');
      expect(lockAcquired).toBe(true);
      
      console.log(`✓ 分布式锁测试通过`);
    });
  });

  describe('支付验证安全性优化验证', () => {
    test('应该正确验证合法支付', async () => {
      const validRequest = {
        transactionId: 'a'.repeat(64), // 64位十六进制
        walletAddress: '0x' + 'a'.repeat(40), // 有效的以太坊地址
        productId: 'speed_boost_2x',
        amount: 100,
        timestamp: Date.now(),
        signature: 'valid_signature'
      };
      
      const startTime = Date.now();
      const result = await securePaymentService.verifyPayment(validRequest);
      const duration = Date.now() - startTime;
      
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('riskScore');
      expect(result.verificationTime).toBeGreaterThan(0);
      expect(duration).toBeLessThan(2000); // 验证应在2秒内完成
      
      console.log(`✓ 支付验证: ${duration}ms, 风险评分: ${result.riskScore}`);
    });

    test('应该正确检测无效支付', async () => {
      const invalidRequest = {
        transactionId: 'invalid',
        walletAddress: 'invalid_address',
        productId: '',
        amount: -100,
        timestamp: Date.now() - 1000000, // 过期时间戳
      };
      
      const result = await securePaymentService.verifyPayment(invalidRequest);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.length).toBeGreaterThan(0);
      
      console.log(`✓ 无效支付检测: ${result.errors!.length}个错误`);
    });
  });

  describe('并发控制和事务优化验证', () => {
    test('应该正确处理并发事务', async () => {
      const concurrentOperations = Array.from({ length: 10 }, (_, i) => 
        concurrencyControlService.executeOptimizedTransaction(async (transaction) => {
          // 模拟数据库操作
          await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
          return `operation_${i}`;
        })
      );
      
      const startTime = Date.now();
      const results = await Promise.all(concurrentOperations);
      const duration = Date.now() - startTime;
      
      expect(results).toHaveLength(10);
      expect(duration).toBeLessThan(2000); // 并发执行应该比串行快
      
      console.log(`✓ 并发事务处理: ${duration}ms`);
    });

    test('应该正确实现乐观锁', async () => {
      const resourceId = 'test_resource';
      let operationCount = 0;
      
      const operations = Array.from({ length: 5 }, () =>
        concurrencyControlService.withOptimisticLock(resourceId, async () => {
          operationCount++;
          await new Promise(resolve => setTimeout(resolve, 50));
          return operationCount;
        })
      );
      
      const results = await Promise.all(operations);
      
      // 由于乐观锁，操作应该串行执行
      expect(results).toEqual([1, 2, 3, 4, 5]);
      
      console.log(`✓ 乐观锁测试通过`);
    });
  });

  describe('性能监控验证', () => {
    test('应该正确收集性能指标', async () => {
      const metrics = await performanceMonitoringService.collectMetrics();
      
      expect(metrics).toHaveProperty('timestamp');
      expect(metrics).toHaveProperty('cpu');
      expect(metrics).toHaveProperty('memory');
      expect(metrics).toHaveProperty('database');
      expect(metrics).toHaveProperty('cache');
      expect(metrics).toHaveProperty('api');
      
      expect(metrics.cpu.usage).toBeGreaterThanOrEqual(0);
      expect(metrics.memory.used).toBeGreaterThan(0);
      
      console.log(`✓ 性能指标收集: CPU ${metrics.cpu.usage.toFixed(2)}%, 内存 ${(metrics.memory.used / 1024 / 1024).toFixed(2)}MB`);
    });

    test('应该正确执行负载测试', async () => {
      const loadTestConfig = {
        duration: 5, // 5秒测试
        concurrentUsers: 5,
        rampUpTime: 1,
        endpoints: [
          { path: '/api/test', method: 'GET', weight: 1 }
        ]
      };
      
      const startTime = Date.now();
      const result = await performanceMonitoringService.runLoadTest(loadTestConfig);
      const duration = Date.now() - startTime;
      
      expect(result.totalRequests).toBeGreaterThan(0);
      expect(result.requestsPerSecond).toBeGreaterThan(0);
      expect(duration).toBeLessThan(10000); // 测试应在10秒内完成
      
      console.log(`✓ 负载测试: ${result.totalRequests}个请求, ${result.requestsPerSecond.toFixed(2)} RPS`);
    }, 15000); // 增加超时时间
  });

  describe('综合性能验证', () => {
    test('应该通过性能回归测试', async () => {
      const regressionResult = await performanceMonitoringService.runRegressionTest();
      
      expect(regressionResult.summary.totalTests).toBeGreaterThan(0);
      expect(regressionResult.summary.failedTests).toBe(0); // 不应该有失败的测试
      
      console.log(`✓ 性能回归测试: ${regressionResult.summary.passedTests}/${regressionResult.summary.totalTests} 通过`);
    });

    test('应该生成完整的性能报告', async () => {
      // 先收集一些指标
      performanceMonitoringService.startMonitoring(1000);
      await new Promise(resolve => setTimeout(resolve, 3000));
      performanceMonitoringService.stopMonitoring();
      
      const report = await performanceMonitoringService.generatePerformanceReport();
      
      expect(report).toHaveProperty('timestamp');
      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('trends');
      expect(report).toHaveProperty('recommendations');
      
      console.log(`✓ 性能报告生成: ${report.recommendations.length}条建议`);
    });
  });
});

/**
 * 设置测试环境
 */
async function setupTestEnvironment(): Promise<void> {
  // 这里可以设置测试数据库、缓存等
  console.log('  🔧 设置测试环境...');
}

/**
 * 清理测试环境
 */
async function cleanupTestEnvironment(): Promise<void> {
  // 清理测试数据
  console.log('  🧹 清理测试环境...');
  
  // 停止监控服务
  performanceMonitoringService.stopMonitoring();
  
  // 清理缓存
  await advancedCacheService.deletePattern('test_*');
}
