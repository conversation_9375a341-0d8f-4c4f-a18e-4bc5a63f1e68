/**
 * 批量计算优化服务
 * 用于优化BigNumber.js的批量计算操作，提升性能
 */

import BigNumber from 'bignumber.js';
import { FarmPlotCalculator, DeliveryLineCalculator, formatToThreeDecimalsNumber } from './bigNumberConfig';

interface BatchCalculationResult<T> {
  results: T[];
  totalTime: number;
  averageTime: number;
}

interface FarmPlotUpgradeData {
  level: number;
  currentSpeed: number;
  currentProduction: number;
  currentCost: number;
}

interface DeliveryLineUpgradeData {
  level: number;
  currentSpeed: number;
  currentUnit: number;
  currentPrice: number;
  currentCost: number;
}

interface FarmPlotCalculationResult {
  level: number;
  baseProduction: number;
  upgradedSpeed: number;
  upgradedProduction: number;
  upgradeCost: number;
  unlockCost: number;
}

interface DeliveryLineCalculationResult {
  level: number;
  upgradedSpeed: number;
  upgradedUnit: number;
  upgradedPrice: number;
  upgradeCost: number;
}

class BatchCalculationService {
  
  /**
   * 批量计算农场区升级数据
   * 使用预计算和缓存优化性能
   */
  batchCalculateFarmPlotUpgrades(
    plotsData: FarmPlotUpgradeData[]
  ): BatchCalculationResult<FarmPlotCalculationResult> {
    const startTime = Date.now();
    const results: FarmPlotCalculationResult[] = [];

    // 预计算常用的倍数和除数
    const speedDivisor = new BigNumber(1.05);
    const productionMultiplier = new BigNumber(1.5);
    const costMultiplier = new BigNumber(1.5);
    const unlockBase = new BigNumber(2000);
    const unlockMultiplier = new BigNumber(2);

    for (const plotData of plotsData) {
      // 使用缓存的基础产量计算
      const baseProduction = FarmPlotCalculator.calculateBaseProduction(plotData.level);
      
      // 批量计算升级后的数值
      const upgradedSpeed = formatToThreeDecimalsNumber(
        new BigNumber(plotData.currentSpeed).dividedBy(speedDivisor)
      );
      
      const upgradedProduction = formatToThreeDecimalsNumber(
        new BigNumber(plotData.currentProduction).multipliedBy(productionMultiplier)
      );
      
      const upgradeCost = formatToThreeDecimalsNumber(
        new BigNumber(plotData.currentCost).multipliedBy(costMultiplier)
      );
      
      // 计算解锁费用（使用预计算的常数）
      const unlockCost = plotData.level === 1 ? 0 : formatToThreeDecimalsNumber(
        unlockBase.multipliedBy(unlockMultiplier.pow(plotData.level - 2))
      );

      results.push({
        level: plotData.level,
        baseProduction,
        upgradedSpeed,
        upgradedProduction,
        upgradeCost,
        unlockCost
      });
    }

    const totalTime = Date.now() - startTime;
    
    return {
      results,
      totalTime,
      averageTime: totalTime / plotsData.length
    };
  }

  /**
   * 批量计算配送线升级数据
   */
  batchCalculateDeliveryLineUpgrades(
    linesData: DeliveryLineUpgradeData[]
  ): BatchCalculationResult<DeliveryLineCalculationResult> {
    const startTime = Date.now();
    const results: DeliveryLineCalculationResult[] = [];

    // 预计算常用的倍数和除数
    const speedDivisor = new BigNumber(1.01);
    const unitMultiplier = new BigNumber(2.0);
    const priceMultiplier = new BigNumber(2.0);
    const costMultiplier = new BigNumber(2.0);

    for (const lineData of linesData) {
      // 批量计算升级后的数值
      const upgradedSpeed = formatToThreeDecimalsNumber(
        new BigNumber(lineData.currentSpeed).dividedBy(speedDivisor)
      );
      
      const upgradedUnit = formatToThreeDecimalsNumber(
        new BigNumber(lineData.currentUnit).multipliedBy(unitMultiplier)
      );
      
      const upgradedPrice = formatToThreeDecimalsNumber(
        new BigNumber(lineData.currentPrice).multipliedBy(priceMultiplier)
      );
      
      const upgradeCost = formatToThreeDecimalsNumber(
        new BigNumber(lineData.currentCost).multipliedBy(costMultiplier)
      );

      results.push({
        level: lineData.level,
        upgradedSpeed,
        upgradedUnit,
        upgradedPrice,
        upgradeCost
      });
    }

    const totalTime = Date.now() - startTime;
    
    return {
      results,
      totalTime,
      averageTime: totalTime / linesData.length
    };
  }

  /**
   * 批量计算VIP效果应用
   * 对多个数值同时应用VIP加成
   */
  batchApplyVipEffects(
    values: number[],
    vipMultiplier: number
  ): BatchCalculationResult<number> {
    const startTime = Date.now();
    const multiplier = new BigNumber(vipMultiplier);
    
    const results = values.map(value => 
      formatToThreeDecimalsNumber(new BigNumber(value).multipliedBy(multiplier))
    );

    const totalTime = Date.now() - startTime;
    
    return {
      results,
      totalTime,
      averageTime: totalTime / values.length
    };
  }

  /**
   * 批量计算速度加成效果
   * 对多个速度值同时应用加成（速度是除法）
   */
  batchApplySpeedBoosts(
    speeds: number[],
    boostMultiplier: number
  ): BatchCalculationResult<number> {
    const startTime = Date.now();
    const divisor = new BigNumber(boostMultiplier);
    
    const results = speeds.map(speed => 
      formatToThreeDecimalsNumber(new BigNumber(speed).dividedBy(divisor))
    );

    const totalTime = Date.now() - startTime;
    
    return {
      results,
      totalTime,
      averageTime: totalTime / speeds.length
    };
  }

  /**
   * 批量计算离线收益
   * 优化大量农场区的离线收益计算
   */
  batchCalculateOfflineEarnings(
    farmPlots: Array<{
      level: number;
      barnCount: number;
      productionSpeed: number;
      vipMultiplier?: number;
    }>,
    offlineSeconds: number
  ): BatchCalculationResult<number> {
    const startTime = Date.now();
    const results: number[] = [];

    // 预计算离线时间的BigNumber
    const offlineTime = new BigNumber(offlineSeconds);

    for (const plot of farmPlots) {
      // 使用缓存的基础产量
      const baseProduction = FarmPlotCalculator.calculateBaseProduction(plot.level);
      
      // 计算实际生产速度（考虑VIP加成）
      const actualSpeed = plot.vipMultiplier ? 
        plot.productionSpeed / plot.vipMultiplier : 
        plot.productionSpeed;
      
      // 计算生产周期数
      const cycles = offlineTime.dividedBy(actualSpeed).integerValue(BigNumber.ROUND_DOWN);
      
      // 计算总产量
      const totalProduction = cycles
        .multipliedBy(baseProduction)
        .multipliedBy(plot.barnCount);
      
      results.push(formatToThreeDecimalsNumber(totalProduction));
    }

    const totalTime = Date.now() - startTime;
    
    return {
      results,
      totalTime,
      averageTime: totalTime / farmPlots.length
    };
  }

  /**
   * 预计算常用数值表
   * 为常用的等级和倍数预计算结果
   */
  precomputeCommonValues(): void {
    console.log('开始预计算常用数值...');
    const startTime = Date.now();

    // 预计算农场区1-20级的基础产量
    for (let level = 1; level <= 20; level++) {
      FarmPlotCalculator.calculateBaseProduction(level);
    }

    // 预计算常用的速度升级值
    const commonSpeeds = [5, 4.762, 4.535, 4.319, 4.114, 3.918, 3.731, 3.553, 3.384, 3.223];
    for (const speed of commonSpeeds) {
      FarmPlotCalculator.calculateUpgradedSpeed(speed);
    }

    // 预计算农场区解锁费用
    for (let plotNumber = 1; plotNumber <= 20; plotNumber++) {
      FarmPlotCalculator.calculateUnlockCost(plotNumber);
    }

    // 预计算配送线常用升级值
    const commonDeliverySpeeds = [5, 4.95, 4.901, 4.852, 4.804];
    for (const speed of commonDeliverySpeeds) {
      DeliveryLineCalculator.calculateUpgradedSpeed(speed);
    }

    const precomputeTime = Date.now() - startTime;
    console.log(`预计算完成，耗时: ${precomputeTime}ms`);
  }

  /**
   * 内存优化：清理计算缓存
   */
  clearCalculationCache(): void {
    // 这里可以调用bigNumberConfig中的缓存清理方法
    console.log('清理计算缓存');
  }

  /**
   * 获取计算性能统计
   */
  getPerformanceStats(): any {
    return {
      timestamp: new Date(),
      message: '批量计算服务运行正常',
      // 这里可以添加更多性能统计信息
    };
  }
}

export default new BatchCalculationService();
