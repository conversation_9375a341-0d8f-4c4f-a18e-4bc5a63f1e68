/**
 * 性能测试工具
 * 用于测试BigNumber.js优化效果和数据库查询性能
 */

import { FarmPlotCalculator, DeliveryLineCalculator } from './bigNumberConfig';
import batchCalculationService from './batchCalculationService';
import OptimizedBigNumberOps, { BigNumberMemoryManager } from './bigNumberPool';
import queryCache from '../services/queryCache';
import batchQueryService from '../services/batchQueryService';

interface PerformanceTestResult {
  testName: string;
  iterations: number;
  totalTime: number;
  averageTime: number;
  memoryBefore: any;
  memoryAfter: any;
  cacheStats?: any;
}

class PerformanceTest {

  /**
   * 测试农场区计算性能
   */
  async testFarmPlotCalculations(iterations: number = 1000): Promise<PerformanceTestResult> {
    const memoryBefore = BigNumberMemoryManager.getMemoryUsage();
    const startTime = Date.now();

    // 测试各种农场区计算
    for (let i = 0; i < iterations; i++) {
      const level = (i % 20) + 1;
      const speed = 5 - (i % 10) * 0.1;
      const production = 1 + (i % 5) * 0.5;
      const cost = 200 + (i % 8) * 50;
      const plotNumber = (i % 20) + 1;

      // 执行各种计算
      FarmPlotCalculator.calculateBaseProduction(level);
      FarmPlotCalculator.calculateUpgradedSpeed(speed);
      FarmPlotCalculator.calculateUpgradedProduction(production);
      FarmPlotCalculator.calculateUpgradeCost(cost);
      FarmPlotCalculator.calculateUnlockCost(plotNumber);
    }

    const totalTime = Date.now() - startTime;
    const memoryAfter = BigNumberMemoryManager.getMemoryUsage();

    return {
      testName: 'Farm Plot Calculations',
      iterations,
      totalTime,
      averageTime: totalTime / iterations,
      memoryBefore,
      memoryAfter,
      cacheStats: memoryAfter.pool
    };
  }

  /**
   * 测试配送线计算性能
   */
  async testDeliveryLineCalculations(iterations: number = 1000): Promise<PerformanceTestResult> {
    const memoryBefore = BigNumberMemoryManager.getMemoryUsage();
    const startTime = Date.now();

    for (let i = 0; i < iterations; i++) {
      const speed = 5 - (i % 10) * 0.05;
      const unit = 5 + (i % 5) * 2;
      const price = 5 + (i % 8) * 1;
      const cost = 500 + (i % 10) * 100;

      // 执行各种计算
      DeliveryLineCalculator.calculateUpgradedSpeed(speed);
      DeliveryLineCalculator.calculateUpgradedUnit(unit);
      DeliveryLineCalculator.calculateUpgradedPrice(price);
      DeliveryLineCalculator.calculateUpgradeCost(cost);
    }

    const totalTime = Date.now() - startTime;
    const memoryAfter = BigNumberMemoryManager.getMemoryUsage();

    return {
      testName: 'Delivery Line Calculations',
      iterations,
      totalTime,
      averageTime: totalTime / iterations,
      memoryBefore,
      memoryAfter,
      cacheStats: memoryAfter.pool
    };
  }

  /**
   * 测试批量计算性能
   */
  async testBatchCalculations(batchSize: number = 100): Promise<PerformanceTestResult> {
    const memoryBefore = BigNumberMemoryManager.getMemoryUsage();
    const startTime = Date.now();

    // 准备测试数据
    const farmPlotsData = Array.from({ length: batchSize }, (_, i) => ({
      level: (i % 20) + 1,
      currentSpeed: 5 - (i % 10) * 0.1,
      currentProduction: 1 + (i % 5) * 0.5,
      currentCost: 200 + (i % 8) * 50
    }));

    const deliveryLinesData = Array.from({ length: batchSize }, (_, i) => ({
      level: (i % 10) + 1,
      currentSpeed: 5 - (i % 10) * 0.05,
      currentUnit: 5 + (i % 5) * 2,
      currentPrice: 5 + (i % 8) * 1,
      currentCost: 500 + (i % 10) * 100
    }));

    // 执行批量计算
    const farmResult = batchCalculationService.batchCalculateFarmPlotUpgrades(farmPlotsData);
    const deliveryResult = batchCalculationService.batchCalculateDeliveryLineUpgrades(deliveryLinesData);

    // 测试VIP效果批量应用
    const values = Array.from({ length: batchSize }, (_, i) => i * 10);
    const vipResult = batchCalculationService.batchApplyVipEffects(values, 1.3);
    const speedResult = batchCalculationService.batchApplySpeedBoosts(values, 2.0);

    const totalTime = Date.now() - startTime;
    const memoryAfter = BigNumberMemoryManager.getMemoryUsage();

    return {
      testName: 'Batch Calculations',
      iterations: batchSize * 4, // 4种不同的批量操作
      totalTime,
      averageTime: totalTime / (batchSize * 4),
      memoryBefore,
      memoryAfter,
      cacheStats: {
        farmCalculationTime: farmResult.totalTime,
        deliveryCalculationTime: deliveryResult.totalTime,
        vipEffectTime: vipResult.totalTime,
        speedBoostTime: speedResult.totalTime
      }
    };
  }

  /**
   * 测试优化的BigNumber操作性能
   */
  async testOptimizedBigNumberOps(iterations: number = 1000): Promise<PerformanceTestResult> {
    const memoryBefore = BigNumberMemoryManager.getMemoryUsage();
    const startTime = Date.now();

    for (let i = 0; i < iterations; i++) {
      const a = Math.random() * 1000;
      const b = Math.random() * 100;

      // 测试优化的运算
      OptimizedBigNumberOps.multiply(a, b);
      OptimizedBigNumberOps.divide(a, b);
      OptimizedBigNumberOps.add(a, b);
      OptimizedBigNumberOps.subtract(a, b);
      OptimizedBigNumberOps.pow(a, 2);
    }

    // 测试批量运算
    const values = Array.from({ length: 100 }, () => Math.random() * 1000);
    OptimizedBigNumberOps.batchMultiply(values, 1.5);
    OptimizedBigNumberOps.batchDivide(values, 2.0);

    const totalTime = Date.now() - startTime;
    const memoryAfter = BigNumberMemoryManager.getMemoryUsage();

    return {
      testName: 'Optimized BigNumber Operations',
      iterations: iterations * 5 + 200, // 5个单独操作 + 2个批量操作
      totalTime,
      averageTime: totalTime / (iterations * 5 + 200),
      memoryBefore,
      memoryAfter,
      cacheStats: memoryAfter.pool
    };
  }

  /**
   * 测试缓存性能
   */
  async testCachePerformance(iterations: number = 500): Promise<PerformanceTestResult> {
    const memoryBefore = process.memoryUsage();
    const startTime = Date.now();

    // 模拟缓存操作
    for (let i = 0; i < iterations; i++) {
      const walletId = (i % 10) + 1; // 模拟10个不同用户
      
      // 测试各种缓存操作
      await queryCache.getVipEffects(walletId, true);
      await queryCache.getBoosterEffects(walletId, true);
      
      // 第二次调用应该命中缓存
      await queryCache.getVipEffects(walletId, true);
      await queryCache.getBoosterEffects(walletId, true);
    }

    const totalTime = Date.now() - startTime;
    const memoryAfter = process.memoryUsage();

    return {
      testName: 'Cache Performance',
      iterations: iterations * 4,
      totalTime,
      averageTime: totalTime / (iterations * 4),
      memoryBefore: {
        heapUsed: Math.round(memoryBefore.heapUsed / 1024 / 1024 * 100) / 100,
        heapTotal: Math.round(memoryBefore.heapTotal / 1024 / 1024 * 100) / 100
      },
      memoryAfter: {
        heapUsed: Math.round(memoryAfter.heapUsed / 1024 / 1024 * 100) / 100,
        heapTotal: Math.round(memoryAfter.heapTotal / 1024 / 1024 * 100) / 100
      }
    };
  }

  /**
   * 运行完整的性能测试套件
   */
  async runFullTestSuite(): Promise<PerformanceTestResult[]> {
    console.log('开始运行性能测试套件...');
    
    const results: PerformanceTestResult[] = [];

    try {
      // 预热缓存
      batchCalculationService.precomputeCommonValues();
      
      console.log('1. 测试农场区计算性能...');
      results.push(await this.testFarmPlotCalculations(1000));

      console.log('2. 测试配送线计算性能...');
      results.push(await this.testDeliveryLineCalculations(1000));

      console.log('3. 测试批量计算性能...');
      results.push(await this.testBatchCalculations(100));

      console.log('4. 测试优化的BigNumber操作...');
      results.push(await this.testOptimizedBigNumberOps(1000));

      console.log('5. 测试缓存性能...');
      results.push(await this.testCachePerformance(500));

      console.log('性能测试套件完成！');
      
      // 输出汇总报告
      this.printSummaryReport(results);
      
    } catch (error) {
      console.error('性能测试失败:', error);
    }

    return results;
  }

  /**
   * 打印汇总报告
   */
  private printSummaryReport(results: PerformanceTestResult[]): void {
    console.log('\n=== 性能测试汇总报告 ===');
    
    results.forEach(result => {
      console.log(`\n${result.testName}:`);
      console.log(`  迭代次数: ${result.iterations}`);
      console.log(`  总耗时: ${result.totalTime}ms`);
      console.log(`  平均耗时: ${result.averageTime.toFixed(3)}ms`);
      
      if (result.memoryBefore && result.memoryAfter) {
        const memoryDiff = result.memoryAfter.memory ? 
          result.memoryAfter.memory.heapUsed - result.memoryBefore.memory.heapUsed :
          result.memoryAfter.heapUsed - result.memoryBefore.heapUsed;
        console.log(`  内存变化: ${memoryDiff > 0 ? '+' : ''}${memoryDiff.toFixed(2)}MB`);
      }
      
      if (result.cacheStats) {
        console.log(`  缓存统计:`, result.cacheStats);
      }
    });

    // 计算总体性能
    const totalTime = results.reduce((sum, r) => sum + r.totalTime, 0);
    const totalIterations = results.reduce((sum, r) => sum + r.iterations, 0);
    
    console.log(`\n总体性能:`);
    console.log(`  总迭代次数: ${totalIterations}`);
    console.log(`  总耗时: ${totalTime}ms`);
    console.log(`  平均每次操作: ${(totalTime / totalIterations).toFixed(3)}ms`);
  }
}

export default new PerformanceTest();
