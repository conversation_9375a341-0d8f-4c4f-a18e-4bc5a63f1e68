/**
 * 性能监控工具
 * 用于监控数据库查询性能、API响应时间等关键指标
 */

import { sequelize } from '../config/db';
import { redis } from '../config/redis';

interface QueryMetrics {
  query: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

interface APIMetrics {
  endpoint: string;
  method: string;
  duration: number;
  statusCode: number;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
}

interface CacheMetrics {
  operation: 'get' | 'set' | 'del';
  key: string;
  hit: boolean;
  duration: number;
  timestamp: Date;
}

class PerformanceMonitor {
  private queryMetrics: QueryMetrics[] = [];
  private apiMetrics: APIMetrics[] = [];
  private cacheMetrics: CacheMetrics[] = [];
  private maxMetricsSize = 1000; // 最大保存的指标数量

  /**
   * 记录数据库查询性能
   */
  recordQuery(query: string, duration: number, success: boolean, error?: string): void {
    const metric: QueryMetrics = {
      query: this.sanitizeQuery(query),
      duration,
      timestamp: new Date(),
      success,
      error
    };

    this.queryMetrics.push(metric);
    this.trimMetrics(this.queryMetrics);

    // 记录慢查询
    if (duration > 1000) { // 超过1秒的查询
      console.warn(`慢查询检测: ${duration}ms`, {
        query: metric.query,
        error: error
      });
    }
  }

  /**
   * 记录API性能
   */
  recordAPI(endpoint: string, method: string, duration: number, statusCode: number, userAgent?: string, ip?: string): void {
    const metric: APIMetrics = {
      endpoint,
      method,
      duration,
      statusCode,
      timestamp: new Date(),
      userAgent,
      ip
    };

    this.apiMetrics.push(metric);
    this.trimMetrics(this.apiMetrics);

    // 记录慢API
    if (duration > 2000) { // 超过2秒的API
      console.warn(`慢API检测: ${duration}ms`, {
        endpoint,
        method,
        statusCode
      });
    }
  }

  /**
   * 记录缓存性能
   */
  recordCache(operation: 'get' | 'set' | 'del', key: string, hit: boolean, duration: number): void {
    const metric: CacheMetrics = {
      operation,
      key: this.sanitizeCacheKey(key),
      hit,
      duration,
      timestamp: new Date()
    };

    this.cacheMetrics.push(metric);
    this.trimMetrics(this.cacheMetrics);
  }

  /**
   * 获取查询性能统计
   */
  getQueryStats(timeRange: number = 3600000): any { // 默认1小时
    const cutoff = new Date(Date.now() - timeRange);
    const recentQueries = this.queryMetrics.filter(m => m.timestamp > cutoff);

    if (recentQueries.length === 0) {
      return { totalQueries: 0, avgDuration: 0, slowQueries: 0, errorRate: 0 };
    }

    const totalQueries = recentQueries.length;
    const avgDuration = recentQueries.reduce((sum, m) => sum + m.duration, 0) / totalQueries;
    const slowQueries = recentQueries.filter(m => m.duration > 1000).length;
    const errorQueries = recentQueries.filter(m => !m.success).length;
    const errorRate = (errorQueries / totalQueries) * 100;

    // 获取最慢的查询
    const slowestQueries = recentQueries
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 5)
      .map(m => ({ query: m.query, duration: m.duration }));

    return {
      totalQueries,
      avgDuration: Math.round(avgDuration),
      slowQueries,
      errorRate: Math.round(errorRate * 100) / 100,
      slowestQueries
    };
  }

  /**
   * 获取API性能统计
   */
  getAPIStats(timeRange: number = 3600000): any { // 默认1小时
    const cutoff = new Date(Date.now() - timeRange);
    const recentAPIs = this.apiMetrics.filter(m => m.timestamp > cutoff);

    if (recentAPIs.length === 0) {
      return { totalRequests: 0, avgDuration: 0, slowRequests: 0, errorRate: 0 };
    }

    const totalRequests = recentAPIs.length;
    const avgDuration = recentAPIs.reduce((sum, m) => sum + m.duration, 0) / totalRequests;
    const slowRequests = recentAPIs.filter(m => m.duration > 2000).length;
    const errorRequests = recentAPIs.filter(m => m.statusCode >= 400).length;
    const errorRate = (errorRequests / totalRequests) * 100;

    // 按端点分组统计
    const endpointStats = this.groupByEndpoint(recentAPIs);

    return {
      totalRequests,
      avgDuration: Math.round(avgDuration),
      slowRequests,
      errorRate: Math.round(errorRate * 100) / 100,
      endpointStats
    };
  }

  /**
   * 获取缓存性能统计
   */
  getCacheStats(timeRange: number = 3600000): any { // 默认1小时
    const cutoff = new Date(Date.now() - timeRange);
    const recentCache = this.cacheMetrics.filter(m => m.timestamp > cutoff);

    if (recentCache.length === 0) {
      return { totalOperations: 0, hitRate: 0, avgDuration: 0 };
    }

    const totalOperations = recentCache.length;
    const hits = recentCache.filter(m => m.hit).length;
    const hitRate = (hits / totalOperations) * 100;
    const avgDuration = recentCache.reduce((sum, m) => sum + m.duration, 0) / totalOperations;

    // 按操作类型分组
    const operationStats = {
      get: recentCache.filter(m => m.operation === 'get').length,
      set: recentCache.filter(m => m.operation === 'set').length,
      del: recentCache.filter(m => m.operation === 'del').length
    };

    return {
      totalOperations,
      hitRate: Math.round(hitRate * 100) / 100,
      avgDuration: Math.round(avgDuration),
      operationStats
    };
  }

  /**
   * 获取数据库连接池状态
   */
  async getConnectionPoolStats(): Promise<any> {
    try {
      const pool = sequelize.connectionManager.pool;
      return {
        size: pool.size,
        available: pool.available,
        using: pool.using,
        waiting: pool.waiting,
        maxConnections: pool.options.max,
        minConnections: pool.options.min
      };
    } catch (error) {
      console.error('获取连接池状态失败:', error);
      return null;
    }
  }

  /**
   * 获取Redis连接状态
   */
  async getRedisStats(): Promise<any> {
    try {
      const info = await redis.info();
      const lines = info.split('\r\n');
      const stats: any = {};

      lines.forEach(line => {
        if (line.includes(':')) {
          const [key, value] = line.split(':');
          if (key && value) {
            stats[key] = isNaN(Number(value)) ? value : Number(value);
          }
        }
      });

      return {
        connected_clients: stats.connected_clients,
        used_memory_human: stats.used_memory_human,
        keyspace_hits: stats.keyspace_hits,
        keyspace_misses: stats.keyspace_misses,
        total_commands_processed: stats.total_commands_processed
      };
    } catch (error) {
      console.error('获取Redis状态失败:', error);
      return null;
    }
  }

  /**
   * 生成性能报告
   */
  async generatePerformanceReport(): Promise<any> {
    const [queryStats, apiStats, cacheStats, poolStats, redisStats] = await Promise.all([
      this.getQueryStats(),
      this.getAPIStats(),
      this.getCacheStats(),
      this.getConnectionPoolStats(),
      this.getRedisStats()
    ]);

    return {
      timestamp: new Date(),
      database: {
        queries: queryStats,
        connectionPool: poolStats
      },
      api: apiStats,
      cache: {
        performance: cacheStats,
        redis: redisStats
      },
      recommendations: this.generateRecommendations(queryStats, apiStats, cacheStats)
    };
  }

  /**
   * 生成性能优化建议
   */
  private generateRecommendations(queryStats: any, apiStats: any, cacheStats: any): string[] {
    const recommendations: string[] = [];

    if (queryStats.avgDuration > 500) {
      recommendations.push('数据库查询平均响应时间较高，建议检查索引和查询优化');
    }

    if (queryStats.errorRate > 5) {
      recommendations.push('数据库查询错误率较高，建议检查查询语句和数据库连接');
    }

    if (apiStats.avgDuration > 1000) {
      recommendations.push('API平均响应时间较高，建议优化业务逻辑和数据库查询');
    }

    if (cacheStats.hitRate < 80) {
      recommendations.push('缓存命中率较低，建议优化缓存策略和预热机制');
    }

    if (recommendations.length === 0) {
      recommendations.push('系统性能良好，继续保持');
    }

    return recommendations;
  }

  /**
   * 清理敏感信息的查询语句
   */
  private sanitizeQuery(query: string): string {
    return query
      .replace(/VALUES\s*\([^)]*\)/gi, 'VALUES (...)')
      .replace(/=\s*'[^']*'/gi, "= '***'")
      .replace(/=\s*"[^"]*"/gi, '= "***"')
      .substring(0, 200); // 限制长度
  }

  /**
   * 清理缓存键名
   */
  private sanitizeCacheKey(key: string): string {
    return key.replace(/:\d+/g, ':***').substring(0, 100);
  }

  /**
   * 按端点分组API统计
   */
  private groupByEndpoint(metrics: APIMetrics[]): any {
    const groups: { [key: string]: APIMetrics[] } = {};
    
    metrics.forEach(metric => {
      const key = `${metric.method} ${metric.endpoint}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(metric);
    });

    const result: any = {};
    Object.keys(groups).forEach(key => {
      const group = groups[key];
      result[key] = {
        count: group.length,
        avgDuration: Math.round(group.reduce((sum, m) => sum + m.duration, 0) / group.length),
        errorRate: Math.round((group.filter(m => m.statusCode >= 400).length / group.length) * 10000) / 100
      };
    });

    return result;
  }

  /**
   * 限制指标数组大小
   */
  private trimMetrics(metrics: any[]): void {
    if (metrics.length > this.maxMetricsSize) {
      metrics.splice(0, metrics.length - this.maxMetricsSize);
    }
  }
}

export default new PerformanceMonitor();
