/**
 * BigNumber对象池
 * 用于减少BigNumber对象的创建和销毁，提升内存使用效率
 */

import BigNumber from 'bignumber.js';

interface PoolStats {
  totalCreated: number;
  totalReused: number;
  currentPoolSize: number;
  maxPoolSize: number;
  hitRate: number;
}

class BigNumberPool {
  private pool: BigNumber[] = [];
  private maxPoolSize: number = 100;
  private totalCreated: number = 0;
  private totalReused: number = 0;

  /**
   * 从池中获取BigNumber实例
   */
  get(value?: number | string | BigNumber): BigNumber {
    let instance: BigNumber;

    if (this.pool.length > 0) {
      // 从池中复用实例
      instance = this.pool.pop()!;
      this.totalReused++;
      
      // 重新设置值
      if (value !== undefined) {
        if (value instanceof BigNumber) {
          instance = instance.plus(value).minus(instance);
        } else {
          instance = new BigNumber(value);
        }
      }
    } else {
      // 创建新实例
      instance = value !== undefined ? new BigNumber(value) : new BigNumber(0);
      this.totalCreated++;
    }

    return instance;
  }

  /**
   * 将BigNumber实例返回到池中
   */
  release(instance: BigNumber): void {
    if (this.pool.length < this.maxPoolSize) {
      // 重置为0以清除之前的值
      instance = instance.minus(instance);
      this.pool.push(instance);
    }
    // 如果池已满，让实例被垃圾回收
  }

  /**
   * 批量获取BigNumber实例
   */
  getBatch(count: number): BigNumber[] {
    const instances: BigNumber[] = [];
    for (let i = 0; i < count; i++) {
      instances.push(this.get());
    }
    return instances;
  }

  /**
   * 批量释放BigNumber实例
   */
  releaseBatch(instances: BigNumber[]): void {
    for (const instance of instances) {
      this.release(instance);
    }
  }

  /**
   * 预热池 - 预先创建一些实例
   */
  warmup(count: number = 50): void {
    const instances = this.getBatch(count);
    this.releaseBatch(instances);
  }

  /**
   * 清空池
   */
  clear(): void {
    this.pool = [];
  }

  /**
   * 设置最大池大小
   */
  setMaxPoolSize(size: number): void {
    this.maxPoolSize = size;
    // 如果当前池大小超过新的最大值，则裁剪
    if (this.pool.length > size) {
      this.pool = this.pool.slice(0, size);
    }
  }

  /**
   * 获取池统计信息
   */
  getStats(): PoolStats {
    const totalOperations = this.totalCreated + this.totalReused;
    const hitRate = totalOperations > 0 ? (this.totalReused / totalOperations) * 100 : 0;

    return {
      totalCreated: this.totalCreated,
      totalReused: this.totalReused,
      currentPoolSize: this.pool.length,
      maxPoolSize: this.maxPoolSize,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.totalCreated = 0;
    this.totalReused = 0;
  }
}

// 全局BigNumber池实例
const bigNumberPool = new BigNumberPool();

/**
 * 优化的BigNumber操作函数
 * 使用对象池来减少内存分配
 */
export class OptimizedBigNumberOps {
  
  /**
   * 优化的乘法运算
   */
  static multiply(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
    const aBN = bigNumberPool.get(a);
    const bBN = bigNumberPool.get(b);
    const result = aBN.multipliedBy(bBN);
    
    // 释放临时对象
    bigNumberPool.release(aBN);
    bigNumberPool.release(bBN);
    
    return result;
  }

  /**
   * 优化的除法运算
   */
  static divide(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
    const aBN = bigNumberPool.get(a);
    const bBN = bigNumberPool.get(b);
    const result = aBN.dividedBy(bBN);
    
    // 释放临时对象
    bigNumberPool.release(aBN);
    bigNumberPool.release(bBN);
    
    return result;
  }

  /**
   * 优化的加法运算
   */
  static add(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
    const aBN = bigNumberPool.get(a);
    const bBN = bigNumberPool.get(b);
    const result = aBN.plus(bBN);
    
    // 释放临时对象
    bigNumberPool.release(aBN);
    bigNumberPool.release(bBN);
    
    return result;
  }

  /**
   * 优化的减法运算
   */
  static subtract(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
    const aBN = bigNumberPool.get(a);
    const bBN = bigNumberPool.get(b);
    const result = aBN.minus(bBN);
    
    // 释放临时对象
    bigNumberPool.release(aBN);
    bigNumberPool.release(bBN);
    
    return result;
  }

  /**
   * 优化的幂运算
   */
  static pow(base: number | string | BigNumber, exponent: number | string | BigNumber): BigNumber {
    const baseBN = bigNumberPool.get(base);
    const expBN = bigNumberPool.get(exponent);
    const result = baseBN.pow(expBN);
    
    // 释放临时对象
    bigNumberPool.release(baseBN);
    bigNumberPool.release(expBN);
    
    return result;
  }

  /**
   * 批量运算优化
   * 对数组中的所有数值执行相同的运算
   */
  static batchMultiply(values: number[], multiplier: number): number[] {
    const multiplierBN = bigNumberPool.get(multiplier);
    const results: number[] = [];

    for (const value of values) {
      const valueBN = bigNumberPool.get(value);
      const result = valueBN.multipliedBy(multiplierBN);
      results.push(parseFloat(result.toFixed(3)));
      bigNumberPool.release(valueBN);
    }

    bigNumberPool.release(multiplierBN);
    return results;
  }

  /**
   * 批量除法运算
   */
  static batchDivide(values: number[], divisor: number): number[] {
    const divisorBN = bigNumberPool.get(divisor);
    const results: number[] = [];

    for (const value of values) {
      const valueBN = bigNumberPool.get(value);
      const result = valueBN.dividedBy(divisorBN);
      results.push(parseFloat(result.toFixed(3)));
      bigNumberPool.release(valueBN);
    }

    bigNumberPool.release(divisorBN);
    return results;
  }
}

/**
 * 内存监控和清理
 */
export class BigNumberMemoryManager {
  
  /**
   * 定期清理池
   */
  static startPeriodicCleanup(intervalMs: number = 300000): void { // 默认5分钟
    setInterval(() => {
      const stats = bigNumberPool.getStats();
      
      // 如果命中率很低，说明池效果不好，清理一部分
      if (stats.hitRate < 30 && stats.currentPoolSize > 20) {
        const keepSize = Math.floor(stats.currentPoolSize * 0.5);
        bigNumberPool.setMaxPoolSize(keepSize);
        bigNumberPool.setMaxPoolSize(100); // 恢复原大小
        console.log(`BigNumber池清理完成，保留 ${keepSize} 个实例`);
      }
      
      // 记录统计信息
      if (process.env.NODE_ENV === 'development') {
        console.log('BigNumber池统计:', stats);
      }
    }, intervalMs);
  }

  /**
   * 获取内存使用情况
   */
  static getMemoryUsage(): any {
    const stats = bigNumberPool.getStats();
    const memUsage = process.memoryUsage();
    
    return {
      pool: stats,
      memory: {
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024 * 100) / 100, // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024 * 100) / 100, // MB
        external: Math.round(memUsage.external / 1024 / 1024 * 100) / 100 // MB
      }
    };
  }

  /**
   * 强制垃圾回收（仅在开发环境）
   */
  static forceGC(): void {
    if (global.gc && process.env.NODE_ENV === 'development') {
      bigNumberPool.clear();
      global.gc();
      console.log('强制垃圾回收完成');
    }
  }
}

// 初始化池
bigNumberPool.warmup();

// 启动定期清理（仅在生产环境）
if (process.env.NODE_ENV === 'production') {
  BigNumberMemoryManager.startPeriodicCleanup();
}

export { bigNumberPool, BigNumberPool };
export default OptimizedBigNumberOps;
