/**
 * 缓存策略配置
 * 为不同类型的数据定义不同的缓存策略
 */

interface CacheStrategy {
  ttl: number;
  maxSize?: number;
  compressionThreshold?: number;
  enableCompression?: boolean;
  enableDistributedLock?: boolean;
  warmupOnStart?: boolean;
  smartInvalidation?: boolean;
}

/**
 * Wolf Fun游戏缓存策略配置
 */
export const CACHE_STRATEGIES = {
  // 用户基础数据 - 中等缓存时间
  USER_WALLET: {
    ttl: 300, // 5分钟
    enableCompression: false,
    enableDistributedLock: true,
    warmupOnStart: false,
    smartInvalidation: true
  } as CacheStrategy,

  // 农场区数据 - 较长缓存时间
  FARM_PLOTS: {
    ttl: 600, // 10分钟
    enableCompression: true,
    compressionThreshold: 512,
    enableDistributedLock: true,
    warmupOnStart: true,
    smartInvalidation: true
  } as CacheStrategy,

  // 配送线数据 - 中等缓存时间
  DELIVERY_LINE: {
    ttl: 300, // 5分钟
    enableCompression: false,
    enableDistributedLock: true,
    warmupOnStart: true,
    smartInvalidation: true
  } as CacheStrategy,

  // VIP效果 - 长缓存时间
  VIP_EFFECTS: {
    ttl: 1800, // 30分钟
    enableCompression: false,
    enableDistributedLock: false,
    warmupOnStart: true,
    smartInvalidation: false
  } as CacheStrategy,

  // 加速器效果 - 短缓存时间
  BOOSTER_EFFECTS: {
    ttl: 60, // 1分钟
    enableCompression: false,
    enableDistributedLock: false,
    warmupOnStart: false,
    smartInvalidation: true
  } as CacheStrategy,

  // 升级计算结果 - 中等缓存时间
  UPGRADE_CALCULATIONS: {
    ttl: 300, // 5分钟
    enableCompression: true,
    compressionThreshold: 256,
    enableDistributedLock: false,
    warmupOnStart: true,
    smartInvalidation: true
  } as CacheStrategy,

  // 离线收益计算 - 短缓存时间
  OFFLINE_EARNINGS: {
    ttl: 120, // 2分钟
    enableCompression: true,
    compressionThreshold: 512,
    enableDistributedLock: true,
    warmupOnStart: false,
    smartInvalidation: true
  } as CacheStrategy,

  // 排行榜数据 - 长缓存时间
  LEADERBOARD: {
    ttl: 1800, // 30分钟
    enableCompression: true,
    compressionThreshold: 1024,
    enableDistributedLock: true,
    warmupOnStart: true,
    smartInvalidation: false
  } as CacheStrategy,

  // 游戏配置数据 - 很长缓存时间
  GAME_CONFIG: {
    ttl: 3600, // 1小时
    enableCompression: false,
    enableDistributedLock: false,
    warmupOnStart: true,
    smartInvalidation: false
  } as CacheStrategy,

  // 统计数据 - 中等缓存时间
  STATISTICS: {
    ttl: 600, // 10分钟
    enableCompression: true,
    compressionThreshold: 512,
    enableDistributedLock: false,
    warmupOnStart: false,
    smartInvalidation: true
  } as CacheStrategy,

  // 临时数据 - 短缓存时间
  TEMPORARY: {
    ttl: 60, // 1分钟
    enableCompression: false,
    enableDistributedLock: false,
    warmupOnStart: false,
    smartInvalidation: false
  } as CacheStrategy,

  // 会话数据 - 中等缓存时间
  SESSION: {
    ttl: 900, // 15分钟
    enableCompression: false,
    enableDistributedLock: false,
    warmupOnStart: false,
    smartInvalidation: true
  } as CacheStrategy
};

/**
 * 缓存键前缀定义
 */
export const CACHE_PREFIXES = {
  USER_WALLET: 'wolf_fun:user_wallet:',
  FARM_PLOTS: 'wolf_fun:farm_plots:',
  FARM_PLOTS_UNLOCKED: 'wolf_fun:farm_plots_unlocked:',
  DELIVERY_LINE: 'wolf_fun:delivery_line:',
  VIP_EFFECTS: 'wolf_fun:vip_effects:',
  BOOSTER_EFFECTS: 'wolf_fun:booster_effects:',
  UPGRADE_CALC_FARM: 'wolf_fun:upgrade_calc:farm_plot:',
  UPGRADE_CALC_DELIVERY: 'wolf_fun:upgrade_calc:delivery_line:',
  OFFLINE_EARNINGS: 'wolf_fun:offline_earnings:',
  LEADERBOARD: 'wolf_fun:leaderboard:',
  GAME_CONFIG: 'wolf_fun:game_config:',
  STATISTICS: 'wolf_fun:statistics:',
  TEMPORARY: 'wolf_fun:temp:',
  SESSION: 'wolf_fun:session:',
  LOCK: 'wolf_fun:lock:',
  ACCESS_COUNT: 'wolf_fun:access:'
};

/**
 * 缓存键生成器
 */
export class CacheKeyGenerator {
  
  /**
   * 生成用户钱包缓存键
   */
  static userWallet(walletId: number): string {
    return `${CACHE_PREFIXES.USER_WALLET}${walletId}`;
  }

  /**
   * 生成农场区缓存键
   */
  static farmPlots(walletId: number): string {
    return `${CACHE_PREFIXES.FARM_PLOTS}${walletId}`;
  }

  /**
   * 生成已解锁农场区缓存键
   */
  static farmPlotsUnlocked(walletId: number): string {
    return `${CACHE_PREFIXES.FARM_PLOTS_UNLOCKED}${walletId}`;
  }

  /**
   * 生成配送线缓存键
   */
  static deliveryLine(walletId: number): string {
    return `${CACHE_PREFIXES.DELIVERY_LINE}${walletId}`;
  }

  /**
   * 生成VIP效果缓存键
   */
  static vipEffects(walletId: number): string {
    return `${CACHE_PREFIXES.VIP_EFFECTS}${walletId}`;
  }

  /**
   * 生成加速器效果缓存键
   */
  static boosterEffects(walletId: number): string {
    return `${CACHE_PREFIXES.BOOSTER_EFFECTS}${walletId}`;
  }

  /**
   * 生成农场区升级计算缓存键
   */
  static farmUpgradeCalc(level: number, speed: number, production: number, cost: number, vipMultiplier: number): string {
    return `${CACHE_PREFIXES.UPGRADE_CALC_FARM}${level}:${speed}:${production}:${cost}:${vipMultiplier}`;
  }

  /**
   * 生成配送线升级计算缓存键
   */
  static deliveryUpgradeCalc(level: number, speed: number, unit: number, price: number, cost: number, vipSpeed: number, vipPrice: number, boost: number): string {
    return `${CACHE_PREFIXES.UPGRADE_CALC_DELIVERY}${level}:${speed}:${unit}:${price}:${cost}:${vipSpeed}:${vipPrice}:${boost}`;
  }

  /**
   * 生成离线收益缓存键
   */
  static offlineEarnings(walletId: number, timestamp: number): string {
    // 使用小时级别的时间戳来缓存
    const hourTimestamp = Math.floor(timestamp / 3600000) * 3600000;
    return `${CACHE_PREFIXES.OFFLINE_EARNINGS}${walletId}:${hourTimestamp}`;
  }

  /**
   * 生成排行榜缓存键
   */
  static leaderboard(type: string, limit: number): string {
    return `${CACHE_PREFIXES.LEADERBOARD}${type}:${limit}`;
  }

  /**
   * 生成游戏配置缓存键
   */
  static gameConfig(configType: string): string {
    return `${CACHE_PREFIXES.GAME_CONFIG}${configType}`;
  }

  /**
   * 生成统计数据缓存键
   */
  static statistics(type: string, period: string): string {
    return `${CACHE_PREFIXES.STATISTICS}${type}:${period}`;
  }

  /**
   * 生成临时数据缓存键
   */
  static temporary(identifier: string): string {
    return `${CACHE_PREFIXES.TEMPORARY}${identifier}`;
  }

  /**
   * 生成会话缓存键
   */
  static session(sessionId: string): string {
    return `${CACHE_PREFIXES.SESSION}${sessionId}`;
  }

  /**
   * 生成分布式锁键
   */
  static lock(resource: string): string {
    return `${CACHE_PREFIXES.LOCK}${resource}`;
  }

  /**
   * 生成访问计数键
   */
  static accessCount(key: string): string {
    return `${CACHE_PREFIXES.ACCESS_COUNT}${key}`;
  }

  /**
   * 获取用户相关的所有缓存键模式
   */
  static userCachePattern(walletId: number): string[] {
    return [
      `${CACHE_PREFIXES.USER_WALLET}${walletId}`,
      `${CACHE_PREFIXES.FARM_PLOTS}${walletId}*`,
      `${CACHE_PREFIXES.DELIVERY_LINE}${walletId}`,
      `${CACHE_PREFIXES.VIP_EFFECTS}${walletId}`,
      `${CACHE_PREFIXES.BOOSTER_EFFECTS}${walletId}`,
      `${CACHE_PREFIXES.OFFLINE_EARNINGS}${walletId}*`
    ];
  }
}

/**
 * 缓存预热配置
 */
export const CACHE_WARMUP_CONFIG = {
  // 启动时预热的数据类型
  STARTUP_WARMUP: [
    'GAME_CONFIG',
    'LEADERBOARD'
  ],
  
  // 用户登录时预热的数据类型
  USER_LOGIN_WARMUP: [
    'USER_WALLET',
    'FARM_PLOTS',
    'DELIVERY_LINE',
    'VIP_EFFECTS'
  ],
  
  // 批量预热的并发数
  WARMUP_CONCURRENCY: 10,
  
  // 预热超时时间（毫秒）
  WARMUP_TIMEOUT: 30000
};

export default {
  CACHE_STRATEGIES,
  CACHE_PREFIXES,
  CacheKeyGenerator,
  CACHE_WARMUP_CONFIG
};
