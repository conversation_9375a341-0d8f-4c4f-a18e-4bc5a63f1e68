// src/config/db.ts
import { Sequelize } from "sequelize";
import dotenv from "dotenv";

dotenv.config();

export const sequelize = new Sequelize(
  process.env.DB_NAME || "wolf_fun_db",
  process.env.DB_USER || "root",
  process.env.DB_PASS || "root",
  {
    host: process.env.DB_HOST || "localhost",
    dialect: "mysql",
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    port: Number(process.env.DB_PORT) || 3669,
    timezone: '+08:00',
    // 优化连接池配置以提升性能
    pool: {
      max: 20,        // 增加最大连接数
      min: 5,         // 设置最小连接数以保持连接池活跃
      acquire: 30000, // 减少获取连接的超时时间
      idle: 10000,    // 连接空闲时间
      evict: 1000,    // 连接回收检查间隔
      handleDisconnects: true // 自动处理断开连接
    },
    dialectOptions: {
      dateStrings: true,
      typeCast: true,
      connectTimeout: 60000,
      acquireTimeout: 30000,
      timeout: 60000,
      // 启用查询缓存
      flags: '+FOUND_ROWS',
      // 优化MySQL连接参数
      ssl: process.env.DB_SSL === 'true' ? {
        rejectUnauthorized: false
      } : false
    },
    define: {
      charset: "utf8mb4",
      collate: "utf8mb4_unicode_ci",
      timestamps: true,
      // 启用索引提示
      indexes: []
    },
    retry: {
      max: 5, // 增加重试次数
      match: [
        /Deadlock/i,
        /Connection acquire timeout/i,
        /Connection lost/i,
        /ECONNRESET/i
      ]
    },
    // 启用查询优化
    benchmark: process.env.NODE_ENV === 'development',
    // 设置查询超时
    query: {
      timeout: 30000
    }
  }
);

export async function connectDB() {
  try {
    await sequelize.sync();
    sequelize.authenticate()
    .then(() => {
      console.log('数据库连接成功');
    })
    .catch(err => {
      console.error('数据库连接失败:', err);
      // 尝试重新连接
      setTimeout(() => {
        sequelize.authenticate();
      }, 5000);
    });

    // 使用 sequelize 实例的 beforeDisconnect 钩子来监听断开连接事件
    Sequelize.beforeDisconnect(() => {
      console.log('数据库连接断开，尝试重连');
      sequelize.authenticate();
    });
    
  
    console.log("MySQL connection has been established successfully.");
  } catch (error) {
    console.error("Unable to connect to the database:", error);
    throw error;
  }
}
